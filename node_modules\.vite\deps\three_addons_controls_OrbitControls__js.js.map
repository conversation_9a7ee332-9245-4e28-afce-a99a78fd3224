{"version": 3, "sources": ["../../three/examples/jsm/controls/OrbitControls.js"], "sourcesContent": ["import {\n\tControls,\n\tMOUSE,\n\tQuaternion,\n\tSpherical,\n\tTOUCH,\n\tVector2,\n\tVector3,\n\t<PERSON><PERSON>,\n\t<PERSON>,\n\tMathUtils\n} from 'three';\n\n/**\n * Fires when the camera has been transformed by the controls.\n *\n * @event OrbitControls#change\n * @type {Object}\n */\nconst _changeEvent = { type: 'change' };\n\n/**\n * Fires when an interaction was initiated.\n *\n * @event OrbitControls#start\n * @type {Object}\n */\nconst _startEvent = { type: 'start' };\n\n/**\n * Fires when an interaction has finished.\n *\n * @event OrbitControls#end\n * @type {Object}\n */\nconst _endEvent = { type: 'end' };\n\nconst _ray = new Ray();\nconst _plane = new Plane();\nconst _TILT_LIMIT = Math.cos( 70 * MathUtils.DEG2RAD );\n\nconst _v = new Vector3();\nconst _twoPI = 2 * Math.PI;\n\nconst _STATE = {\n\tNONE: - 1,\n\tROTATE: 0,\n\tDOLLY: 1,\n\tPAN: 2,\n\tTOUCH_ROTATE: 3,\n\tTOUCH_PAN: 4,\n\tTOUCH_DOLLY_PAN: 5,\n\tTOUCH_DOLLY_ROTATE: 6\n};\nconst _EPS = 0.000001;\n\n\n/**\n * Orbit controls allow the camera to orbit around a target.\n *\n * OrbitControls performs orbiting, dollying (zooming), and panning. Unlike {@link TrackballControls},\n * it maintains the \"up\" direction `object.up` (+Y by default).\n *\n * - Orbit: Left mouse / touch: one-finger move.\n * - Zoom: Middle mouse, or mousewheel / touch: two-finger spread or squish.\n * - Pan: Right mouse, or left mouse + ctrl/meta/shiftKey, or arrow keys / touch: two-finger move.\n *\n * ```js\n * const controls = new OrbitControls( camera, renderer.domElement );\n *\n * // controls.update() must be called after any manual changes to the camera's transform\n * camera.position.set( 0, 20, 100 );\n * controls.update();\n *\n * function animate() {\n *\n * \t// required if controls.enableDamping or controls.autoRotate are set to true\n * \tcontrols.update();\n *\n * \trenderer.render( scene, camera );\n *\n * }\n * ```\n *\n * @augments Controls\n * @three_import import { OrbitControls } from 'three/addons/controls/OrbitControls.js';\n */\nclass OrbitControls extends Controls {\n\n\t/**\n\t * Constructs a new controls instance.\n\t *\n\t * @param {Object3D} object - The object that is managed by the controls.\n\t * @param {?HTMLDOMElement} domElement - The HTML element used for event listeners.\n\t */\n\tconstructor( object, domElement = null ) {\n\n\t\tsuper( object, domElement );\n\n\t\tthis.state = _STATE.NONE;\n\n\t\t/**\n\t\t * The focus point of the controls, the `object` orbits around this.\n\t\t * It can be updated manually at any point to change the focus of the controls.\n\t\t *\n\t\t * @type {Vector3}\n\t\t */\n\t\tthis.target = new Vector3();\n\n\t\t/**\n\t\t * The focus point of the `minTargetRadius` and `maxTargetRadius` limits.\n\t\t * It can be updated manually at any point to change the center of interest\n\t\t * for the `target`.\n\t\t *\n\t\t * @type {Vector3}\n\t\t */\n\t\tthis.cursor = new Vector3();\n\n\t\t/**\n\t\t * How far you can dolly in (perspective camera only).\n\t\t *\n\t\t * @type {number}\n\t\t * @default 0\n\t\t */\n\t\tthis.minDistance = 0;\n\n\t\t/**\n\t\t * How far you can dolly out (perspective camera only).\n\t\t *\n\t\t * @type {number}\n\t\t * @default Infinity\n\t\t */\n\t\tthis.maxDistance = Infinity;\n\n\t\t/**\n\t\t * How far you can zoom in (orthographic camera only).\n\t\t *\n\t\t * @type {number}\n\t\t * @default 0\n\t\t */\n\t\tthis.minZoom = 0;\n\n\t\t/**\n\t\t * How far you can zoom out (orthographic camera only).\n\t\t *\n\t\t * @type {number}\n\t\t * @default Infinity\n\t\t */\n\t\tthis.maxZoom = Infinity;\n\n\t\t/**\n\t\t * How close you can get the target to the 3D `cursor`.\n\t\t *\n\t\t * @type {number}\n\t\t * @default 0\n\t\t */\n\t\tthis.minTargetRadius = 0;\n\n\t\t/**\n\t\t * How far you can move the target from the 3D `cursor`.\n\t\t *\n\t\t * @type {number}\n\t\t * @default Infinity\n\t\t */\n\t\tthis.maxTargetRadius = Infinity;\n\n\t\t/**\n\t\t * How far you can orbit vertically, lower limit. Range is `[0, Math.PI]` radians.\n\t\t *\n\t\t * @type {number}\n\t\t * @default 0\n\t\t */\n\t\tthis.minPolarAngle = 0;\n\n\t\t/**\n\t\t * How far you can orbit vertically, upper limit. Range is `[0, Math.PI]` radians.\n\t\t *\n\t\t * @type {number}\n\t\t * @default Math.PI\n\t\t */\n\t\tthis.maxPolarAngle = Math.PI;\n\n\t\t/**\n\t\t * How far you can orbit horizontally, lower limit. If set, the interval `[ min, max ]`\n\t\t * must be a sub-interval of `[ - 2 PI, 2 PI ]`, with `( max - min < 2 PI )`.\n\t\t *\n\t\t * @type {number}\n\t\t * @default -Infinity\n\t\t */\n\t\tthis.minAzimuthAngle = - Infinity;\n\n\t\t/**\n\t\t * How far you can orbit horizontally, upper limit. If set, the interval `[ min, max ]`\n\t\t * must be a sub-interval of `[ - 2 PI, 2 PI ]`, with `( max - min < 2 PI )`.\n\t\t *\n\t\t * @type {number}\n\t\t * @default -Infinity\n\t\t */\n\t\tthis.maxAzimuthAngle = Infinity;\n\n\t\t/**\n\t\t * Set to `true` to enable damping (inertia), which can be used to give a sense of weight\n\t\t * to the controls. Note that if this is enabled, you must call `update()` in your animation\n\t\t * loop.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default false\n\t\t */\n\t\tthis.enableDamping = false;\n\n\t\t/**\n\t\t * The damping inertia used if `enableDamping` is set to `true`.\n\t\t *\n\t\t * Note that for this to work, you must call `update()` in your animation loop.\n\t\t *\n\t\t * @type {number}\n\t\t * @default 0.05\n\t\t */\n\t\tthis.dampingFactor = 0.05;\n\n\t\t/**\n\t\t * Enable or disable zooming (dollying) of the camera.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default true\n\t\t */\n\t\tthis.enableZoom = true;\n\n\t\t/**\n\t\t * Speed of zooming / dollying.\n\t\t *\n\t\t * @type {number}\n\t\t * @default 1\n\t\t */\n\t\tthis.zoomSpeed = 1.0;\n\n\t\t/**\n\t\t * Enable or disable horizontal and vertical rotation of the camera.\n\t\t *\n\t\t * Note that it is possible to disable a single axis by setting the min and max of the\n\t\t * `minPolarAngle` or `minAzimuthAngle` to the same value, which will cause the vertical\n\t\t * or horizontal rotation to be fixed at that value.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default true\n\t\t */\n\t\tthis.enableRotate = true;\n\n\t\t/**\n\t\t * Speed of rotation.\n\t\t *\n\t\t * @type {number}\n\t\t * @default 1\n\t\t */\n\t\tthis.rotateSpeed = 1.0;\n\n\t\t/**\n\t\t * How fast to rotate the camera when the keyboard is used.\n\t\t *\n\t\t * @type {number}\n\t\t * @default 1\n\t\t */\n\t\tthis.keyRotateSpeed = 1.0;\n\n\t\t/**\n\t\t * Enable or disable camera panning.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default true\n\t\t */\n\t\tthis.enablePan = true;\n\n\t\t/**\n\t\t * Speed of panning.\n\t\t *\n\t\t * @type {number}\n\t\t * @default 1\n\t\t */\n\t\tthis.panSpeed = 1.0;\n\n\t\t/**\n\t\t * Defines how the camera's position is translated when panning. If `true`, the camera pans\n\t\t * in screen space. Otherwise, the camera pans in the plane orthogonal to the camera's up\n\t\t * direction.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default true\n\t\t */\n\t\tthis.screenSpacePanning = true;\n\n\t\t/**\n\t\t * How fast to pan the camera when the keyboard is used in\n\t\t * pixels per keypress.\n\t\t *\n\t\t * @type {number}\n\t\t * @default 7\n\t\t */\n\t\tthis.keyPanSpeed = 7.0;\n\n\t\t/**\n\t\t * Setting this property to `true` allows to zoom to the cursor's position.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default false\n\t\t */\n\t\tthis.zoomToCursor = false;\n\n\t\t/**\n\t\t * Set to true to automatically rotate around the target\n\t\t *\n\t\t * Note that if this is enabled, you must call `update()` in your animation loop.\n\t\t * If you want the auto-rotate speed to be independent of the frame rate (the refresh\n\t\t * rate of the display), you must pass the time `deltaTime`, in seconds, to `update()`.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default false\n\t\t */\n\t\tthis.autoRotate = false;\n\n\t\t/**\n\t\t * How fast to rotate around the target if `autoRotate` is `true`. The default  equates to 30 seconds\n\t\t * per orbit at 60fps.\n\t\t *\n\t\t * Note that if `autoRotate` is enabled, you must call `update()` in your animation loop.\n\t\t *\n\t\t * @type {number}\n\t\t * @default 2\n\t\t */\n\t\tthis.autoRotateSpeed = 2.0;\n\n\t\t/**\n\t\t * This object contains references to the keycodes for controlling camera panning.\n\t\t *\n\t\t * ```js\n\t\t * controls.keys = {\n\t\t * \tLEFT: 'ArrowLeft', //left arrow\n\t\t * \tUP: 'ArrowUp', // up arrow\n\t\t * \tRIGHT: 'ArrowRight', // right arrow\n\t\t * \tBOTTOM: 'ArrowDown' // down arrow\n\t\t * }\n\t\t * ```\n\t\t * @type {Object}\n\t\t */\n\t\tthis.keys = { LEFT: 'ArrowLeft', UP: 'ArrowUp', RIGHT: 'ArrowRight', BOTTOM: 'ArrowDown' };\n\n\t\t/**\n\t\t * This object contains references to the mouse actions used by the controls.\n\t\t *\n\t\t * ```js\n\t\t * controls.mouseButtons = {\n\t\t * \tLEFT: THREE.MOUSE.ROTATE,\n\t\t * \tMIDDLE: THREE.MOUSE.DOLLY,\n\t\t * \tRIGHT: THREE.MOUSE.PAN\n\t\t * }\n\t\t * ```\n\t\t * @type {Object}\n\t\t */\n\t\tthis.mouseButtons = { LEFT: MOUSE.ROTATE, MIDDLE: MOUSE.DOLLY, RIGHT: MOUSE.PAN };\n\n\t\t/**\n\t\t * This object contains references to the touch actions used by the controls.\n\t\t *\n\t\t * ```js\n\t\t * controls.mouseButtons = {\n\t\t * \tONE: THREE.TOUCH.ROTATE,\n\t\t * \tTWO: THREE.TOUCH.DOLLY_PAN\n\t\t * }\n\t\t * ```\n\t\t * @type {Object}\n\t\t */\n\t\tthis.touches = { ONE: TOUCH.ROTATE, TWO: TOUCH.DOLLY_PAN };\n\n\t\t/**\n\t\t * Used internally by `saveState()` and `reset()`.\n\t\t *\n\t\t * @type {Vector3}\n\t\t */\n\t\tthis.target0 = this.target.clone();\n\n\t\t/**\n\t\t * Used internally by `saveState()` and `reset()`.\n\t\t *\n\t\t * @type {Vector3}\n\t\t */\n\t\tthis.position0 = this.object.position.clone();\n\n\t\t/**\n\t\t * Used internally by `saveState()` and `reset()`.\n\t\t *\n\t\t * @type {number}\n\t\t */\n\t\tthis.zoom0 = this.object.zoom;\n\n\t\t// the target DOM element for key events\n\t\tthis._domElementKeyEvents = null;\n\n\t\t// internals\n\n\t\tthis._lastPosition = new Vector3();\n\t\tthis._lastQuaternion = new Quaternion();\n\t\tthis._lastTargetPosition = new Vector3();\n\n\t\t// so camera.up is the orbit axis\n\t\tthis._quat = new Quaternion().setFromUnitVectors( object.up, new Vector3( 0, 1, 0 ) );\n\t\tthis._quatInverse = this._quat.clone().invert();\n\n\t\t// current position in spherical coordinates\n\t\tthis._spherical = new Spherical();\n\t\tthis._sphericalDelta = new Spherical();\n\n\t\tthis._scale = 1;\n\t\tthis._panOffset = new Vector3();\n\n\t\tthis._rotateStart = new Vector2();\n\t\tthis._rotateEnd = new Vector2();\n\t\tthis._rotateDelta = new Vector2();\n\n\t\tthis._panStart = new Vector2();\n\t\tthis._panEnd = new Vector2();\n\t\tthis._panDelta = new Vector2();\n\n\t\tthis._dollyStart = new Vector2();\n\t\tthis._dollyEnd = new Vector2();\n\t\tthis._dollyDelta = new Vector2();\n\n\t\tthis._dollyDirection = new Vector3();\n\t\tthis._mouse = new Vector2();\n\t\tthis._performCursorZoom = false;\n\n\t\tthis._pointers = [];\n\t\tthis._pointerPositions = {};\n\n\t\tthis._controlActive = false;\n\n\t\t// event listeners\n\n\t\tthis._onPointerMove = onPointerMove.bind( this );\n\t\tthis._onPointerDown = onPointerDown.bind( this );\n\t\tthis._onPointerUp = onPointerUp.bind( this );\n\t\tthis._onContextMenu = onContextMenu.bind( this );\n\t\tthis._onMouseWheel = onMouseWheel.bind( this );\n\t\tthis._onKeyDown = onKeyDown.bind( this );\n\n\t\tthis._onTouchStart = onTouchStart.bind( this );\n\t\tthis._onTouchMove = onTouchMove.bind( this );\n\n\t\tthis._onMouseDown = onMouseDown.bind( this );\n\t\tthis._onMouseMove = onMouseMove.bind( this );\n\n\t\tthis._interceptControlDown = interceptControlDown.bind( this );\n\t\tthis._interceptControlUp = interceptControlUp.bind( this );\n\n\t\t//\n\n\t\tif ( this.domElement !== null ) {\n\n\t\t\tthis.connect( this.domElement );\n\n\t\t}\n\n\t\tthis.update();\n\n\t}\n\n\tconnect( element ) {\n\n\t\tsuper.connect( element );\n\n\t\tthis.domElement.addEventListener( 'pointerdown', this._onPointerDown );\n\t\tthis.domElement.addEventListener( 'pointercancel', this._onPointerUp );\n\n\t\tthis.domElement.addEventListener( 'contextmenu', this._onContextMenu );\n\t\tthis.domElement.addEventListener( 'wheel', this._onMouseWheel, { passive: false } );\n\n\t\tconst document = this.domElement.getRootNode(); // offscreen canvas compatibility\n\t\tdocument.addEventListener( 'keydown', this._interceptControlDown, { passive: true, capture: true } );\n\n\t\tthis.domElement.style.touchAction = 'none'; // disable touch scroll\n\n\t}\n\n\tdisconnect() {\n\n\t\tthis.domElement.removeEventListener( 'pointerdown', this._onPointerDown );\n\t\tthis.domElement.removeEventListener( 'pointermove', this._onPointerMove );\n\t\tthis.domElement.removeEventListener( 'pointerup', this._onPointerUp );\n\t\tthis.domElement.removeEventListener( 'pointercancel', this._onPointerUp );\n\n\t\tthis.domElement.removeEventListener( 'wheel', this._onMouseWheel );\n\t\tthis.domElement.removeEventListener( 'contextmenu', this._onContextMenu );\n\n\t\tthis.stopListenToKeyEvents();\n\n\t\tconst document = this.domElement.getRootNode(); // offscreen canvas compatibility\n\t\tdocument.removeEventListener( 'keydown', this._interceptControlDown, { capture: true } );\n\n\t\tthis.domElement.style.touchAction = 'auto';\n\n\t}\n\n\tdispose() {\n\n\t\tthis.disconnect();\n\n\t}\n\n\t/**\n\t * Get the current vertical rotation, in radians.\n\t *\n\t * @return {number} The current vertical rotation, in radians.\n\t */\n\tgetPolarAngle() {\n\n\t\treturn this._spherical.phi;\n\n\t}\n\n\t/**\n\t * Get the current horizontal rotation, in radians.\n\t *\n\t * @return {number} The current horizontal rotation, in radians.\n\t */\n\tgetAzimuthalAngle() {\n\n\t\treturn this._spherical.theta;\n\n\t}\n\n\t/**\n\t * Returns the distance from the camera to the target.\n\t *\n\t * @return {number} The distance from the camera to the target.\n\t */\n\tgetDistance() {\n\n\t\treturn this.object.position.distanceTo( this.target );\n\n\t}\n\n\t/**\n\t * Adds key event listeners to the given DOM element.\n\t * `window` is a recommended argument for using this method.\n\t *\n\t * @param {HTMLDOMElement} domElement - The DOM element\n\t */\n\tlistenToKeyEvents( domElement ) {\n\n\t\tdomElement.addEventListener( 'keydown', this._onKeyDown );\n\t\tthis._domElementKeyEvents = domElement;\n\n\t}\n\n\t/**\n\t * Removes the key event listener previously defined with `listenToKeyEvents()`.\n\t */\n\tstopListenToKeyEvents() {\n\n\t\tif ( this._domElementKeyEvents !== null ) {\n\n\t\t\tthis._domElementKeyEvents.removeEventListener( 'keydown', this._onKeyDown );\n\t\t\tthis._domElementKeyEvents = null;\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Save the current state of the controls. This can later be recovered with `reset()`.\n\t */\n\tsaveState() {\n\n\t\tthis.target0.copy( this.target );\n\t\tthis.position0.copy( this.object.position );\n\t\tthis.zoom0 = this.object.zoom;\n\n\t}\n\n\t/**\n\t * Reset the controls to their state from either the last time the `saveState()`\n\t * was called, or the initial state.\n\t */\n\treset() {\n\n\t\tthis.target.copy( this.target0 );\n\t\tthis.object.position.copy( this.position0 );\n\t\tthis.object.zoom = this.zoom0;\n\n\t\tthis.object.updateProjectionMatrix();\n\t\tthis.dispatchEvent( _changeEvent );\n\n\t\tthis.update();\n\n\t\tthis.state = _STATE.NONE;\n\n\t}\n\n\tupdate( deltaTime = null ) {\n\n\t\tconst position = this.object.position;\n\n\t\t_v.copy( position ).sub( this.target );\n\n\t\t// rotate offset to \"y-axis-is-up\" space\n\t\t_v.applyQuaternion( this._quat );\n\n\t\t// angle from z-axis around y-axis\n\t\tthis._spherical.setFromVector3( _v );\n\n\t\tif ( this.autoRotate && this.state === _STATE.NONE ) {\n\n\t\t\tthis._rotateLeft( this._getAutoRotationAngle( deltaTime ) );\n\n\t\t}\n\n\t\tif ( this.enableDamping ) {\n\n\t\t\tthis._spherical.theta += this._sphericalDelta.theta * this.dampingFactor;\n\t\t\tthis._spherical.phi += this._sphericalDelta.phi * this.dampingFactor;\n\n\t\t} else {\n\n\t\t\tthis._spherical.theta += this._sphericalDelta.theta;\n\t\t\tthis._spherical.phi += this._sphericalDelta.phi;\n\n\t\t}\n\n\t\t// restrict theta to be between desired limits\n\n\t\tlet min = this.minAzimuthAngle;\n\t\tlet max = this.maxAzimuthAngle;\n\n\t\tif ( isFinite( min ) && isFinite( max ) ) {\n\n\t\t\tif ( min < - Math.PI ) min += _twoPI; else if ( min > Math.PI ) min -= _twoPI;\n\n\t\t\tif ( max < - Math.PI ) max += _twoPI; else if ( max > Math.PI ) max -= _twoPI;\n\n\t\t\tif ( min <= max ) {\n\n\t\t\t\tthis._spherical.theta = Math.max( min, Math.min( max, this._spherical.theta ) );\n\n\t\t\t} else {\n\n\t\t\t\tthis._spherical.theta = ( this._spherical.theta > ( min + max ) / 2 ) ?\n\t\t\t\t\tMath.max( min, this._spherical.theta ) :\n\t\t\t\t\tMath.min( max, this._spherical.theta );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// restrict phi to be between desired limits\n\t\tthis._spherical.phi = Math.max( this.minPolarAngle, Math.min( this.maxPolarAngle, this._spherical.phi ) );\n\n\t\tthis._spherical.makeSafe();\n\n\n\t\t// move target to panned location\n\n\t\tif ( this.enableDamping === true ) {\n\n\t\t\tthis.target.addScaledVector( this._panOffset, this.dampingFactor );\n\n\t\t} else {\n\n\t\t\tthis.target.add( this._panOffset );\n\n\t\t}\n\n\t\t// Limit the target distance from the cursor to create a sphere around the center of interest\n\t\tthis.target.sub( this.cursor );\n\t\tthis.target.clampLength( this.minTargetRadius, this.maxTargetRadius );\n\t\tthis.target.add( this.cursor );\n\n\t\tlet zoomChanged = false;\n\t\t// adjust the camera position based on zoom only if we're not zooming to the cursor or if it's an ortho camera\n\t\t// we adjust zoom later in these cases\n\t\tif ( this.zoomToCursor && this._performCursorZoom || this.object.isOrthographicCamera ) {\n\n\t\t\tthis._spherical.radius = this._clampDistance( this._spherical.radius );\n\n\t\t} else {\n\n\t\t\tconst prevRadius = this._spherical.radius;\n\t\t\tthis._spherical.radius = this._clampDistance( this._spherical.radius * this._scale );\n\t\t\tzoomChanged = prevRadius != this._spherical.radius;\n\n\t\t}\n\n\t\t_v.setFromSpherical( this._spherical );\n\n\t\t// rotate offset back to \"camera-up-vector-is-up\" space\n\t\t_v.applyQuaternion( this._quatInverse );\n\n\t\tposition.copy( this.target ).add( _v );\n\n\t\tthis.object.lookAt( this.target );\n\n\t\tif ( this.enableDamping === true ) {\n\n\t\t\tthis._sphericalDelta.theta *= ( 1 - this.dampingFactor );\n\t\t\tthis._sphericalDelta.phi *= ( 1 - this.dampingFactor );\n\n\t\t\tthis._panOffset.multiplyScalar( 1 - this.dampingFactor );\n\n\t\t} else {\n\n\t\t\tthis._sphericalDelta.set( 0, 0, 0 );\n\n\t\t\tthis._panOffset.set( 0, 0, 0 );\n\n\t\t}\n\n\t\t// adjust camera position\n\t\tif ( this.zoomToCursor && this._performCursorZoom ) {\n\n\t\t\tlet newRadius = null;\n\t\t\tif ( this.object.isPerspectiveCamera ) {\n\n\t\t\t\t// move the camera down the pointer ray\n\t\t\t\t// this method avoids floating point error\n\t\t\t\tconst prevRadius = _v.length();\n\t\t\t\tnewRadius = this._clampDistance( prevRadius * this._scale );\n\n\t\t\t\tconst radiusDelta = prevRadius - newRadius;\n\t\t\t\tthis.object.position.addScaledVector( this._dollyDirection, radiusDelta );\n\t\t\t\tthis.object.updateMatrixWorld();\n\n\t\t\t\tzoomChanged = !! radiusDelta;\n\n\t\t\t} else if ( this.object.isOrthographicCamera ) {\n\n\t\t\t\t// adjust the ortho camera position based on zoom changes\n\t\t\t\tconst mouseBefore = new Vector3( this._mouse.x, this._mouse.y, 0 );\n\t\t\t\tmouseBefore.unproject( this.object );\n\n\t\t\t\tconst prevZoom = this.object.zoom;\n\t\t\t\tthis.object.zoom = Math.max( this.minZoom, Math.min( this.maxZoom, this.object.zoom / this._scale ) );\n\t\t\t\tthis.object.updateProjectionMatrix();\n\n\t\t\t\tzoomChanged = prevZoom !== this.object.zoom;\n\n\t\t\t\tconst mouseAfter = new Vector3( this._mouse.x, this._mouse.y, 0 );\n\t\t\t\tmouseAfter.unproject( this.object );\n\n\t\t\t\tthis.object.position.sub( mouseAfter ).add( mouseBefore );\n\t\t\t\tthis.object.updateMatrixWorld();\n\n\t\t\t\tnewRadius = _v.length();\n\n\t\t\t} else {\n\n\t\t\t\tconsole.warn( 'WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled.' );\n\t\t\t\tthis.zoomToCursor = false;\n\n\t\t\t}\n\n\t\t\t// handle the placement of the target\n\t\t\tif ( newRadius !== null ) {\n\n\t\t\t\tif ( this.screenSpacePanning ) {\n\n\t\t\t\t\t// position the orbit target in front of the new camera position\n\t\t\t\t\tthis.target.set( 0, 0, - 1 )\n\t\t\t\t\t\t.transformDirection( this.object.matrix )\n\t\t\t\t\t\t.multiplyScalar( newRadius )\n\t\t\t\t\t\t.add( this.object.position );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// get the ray and translation plane to compute target\n\t\t\t\t\t_ray.origin.copy( this.object.position );\n\t\t\t\t\t_ray.direction.set( 0, 0, - 1 ).transformDirection( this.object.matrix );\n\n\t\t\t\t\t// if the camera is 20 degrees above the horizon then don't adjust the focus target to avoid\n\t\t\t\t\t// extremely large values\n\t\t\t\t\tif ( Math.abs( this.object.up.dot( _ray.direction ) ) < _TILT_LIMIT ) {\n\n\t\t\t\t\t\tthis.object.lookAt( this.target );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\t_plane.setFromNormalAndCoplanarPoint( this.object.up, this.target );\n\t\t\t\t\t\t_ray.intersectPlane( _plane, this.target );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t} else if ( this.object.isOrthographicCamera ) {\n\n\t\t\tconst prevZoom = this.object.zoom;\n\t\t\tthis.object.zoom = Math.max( this.minZoom, Math.min( this.maxZoom, this.object.zoom / this._scale ) );\n\n\t\t\tif ( prevZoom !== this.object.zoom ) {\n\n\t\t\t\tthis.object.updateProjectionMatrix();\n\t\t\t\tzoomChanged = true;\n\n\t\t\t}\n\n\t\t}\n\n\t\tthis._scale = 1;\n\t\tthis._performCursorZoom = false;\n\n\t\t// update condition is:\n\t\t// min(camera displacement, camera rotation in radians)^2 > EPS\n\t\t// using small-angle approximation cos(x/2) = 1 - x^2 / 8\n\n\t\tif ( zoomChanged ||\n\t\t\tthis._lastPosition.distanceToSquared( this.object.position ) > _EPS ||\n\t\t\t8 * ( 1 - this._lastQuaternion.dot( this.object.quaternion ) ) > _EPS ||\n\t\t\tthis._lastTargetPosition.distanceToSquared( this.target ) > _EPS ) {\n\n\t\t\tthis.dispatchEvent( _changeEvent );\n\n\t\t\tthis._lastPosition.copy( this.object.position );\n\t\t\tthis._lastQuaternion.copy( this.object.quaternion );\n\t\t\tthis._lastTargetPosition.copy( this.target );\n\n\t\t\treturn true;\n\n\t\t}\n\n\t\treturn false;\n\n\t}\n\n\t_getAutoRotationAngle( deltaTime ) {\n\n\t\tif ( deltaTime !== null ) {\n\n\t\t\treturn ( _twoPI / 60 * this.autoRotateSpeed ) * deltaTime;\n\n\t\t} else {\n\n\t\t\treturn _twoPI / 60 / 60 * this.autoRotateSpeed;\n\n\t\t}\n\n\t}\n\n\t_getZoomScale( delta ) {\n\n\t\tconst normalizedDelta = Math.abs( delta * 0.01 );\n\t\treturn Math.pow( 0.95, this.zoomSpeed * normalizedDelta );\n\n\t}\n\n\t_rotateLeft( angle ) {\n\n\t\tthis._sphericalDelta.theta -= angle;\n\n\t}\n\n\t_rotateUp( angle ) {\n\n\t\tthis._sphericalDelta.phi -= angle;\n\n\t}\n\n\t_panLeft( distance, objectMatrix ) {\n\n\t\t_v.setFromMatrixColumn( objectMatrix, 0 ); // get X column of objectMatrix\n\t\t_v.multiplyScalar( - distance );\n\n\t\tthis._panOffset.add( _v );\n\n\t}\n\n\t_panUp( distance, objectMatrix ) {\n\n\t\tif ( this.screenSpacePanning === true ) {\n\n\t\t\t_v.setFromMatrixColumn( objectMatrix, 1 );\n\n\t\t} else {\n\n\t\t\t_v.setFromMatrixColumn( objectMatrix, 0 );\n\t\t\t_v.crossVectors( this.object.up, _v );\n\n\t\t}\n\n\t\t_v.multiplyScalar( distance );\n\n\t\tthis._panOffset.add( _v );\n\n\t}\n\n\t// deltaX and deltaY are in pixels; right and down are positive\n\t_pan( deltaX, deltaY ) {\n\n\t\tconst element = this.domElement;\n\n\t\tif ( this.object.isPerspectiveCamera ) {\n\n\t\t\t// perspective\n\t\t\tconst position = this.object.position;\n\t\t\t_v.copy( position ).sub( this.target );\n\t\t\tlet targetDistance = _v.length();\n\n\t\t\t// half of the fov is center to top of screen\n\t\t\ttargetDistance *= Math.tan( ( this.object.fov / 2 ) * Math.PI / 180.0 );\n\n\t\t\t// we use only clientHeight here so aspect ratio does not distort speed\n\t\t\tthis._panLeft( 2 * deltaX * targetDistance / element.clientHeight, this.object.matrix );\n\t\t\tthis._panUp( 2 * deltaY * targetDistance / element.clientHeight, this.object.matrix );\n\n\t\t} else if ( this.object.isOrthographicCamera ) {\n\n\t\t\t// orthographic\n\t\t\tthis._panLeft( deltaX * ( this.object.right - this.object.left ) / this.object.zoom / element.clientWidth, this.object.matrix );\n\t\t\tthis._panUp( deltaY * ( this.object.top - this.object.bottom ) / this.object.zoom / element.clientHeight, this.object.matrix );\n\n\t\t} else {\n\n\t\t\t// camera neither orthographic nor perspective\n\t\t\tconsole.warn( 'WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.' );\n\t\t\tthis.enablePan = false;\n\n\t\t}\n\n\t}\n\n\t_dollyOut( dollyScale ) {\n\n\t\tif ( this.object.isPerspectiveCamera || this.object.isOrthographicCamera ) {\n\n\t\t\tthis._scale /= dollyScale;\n\n\t\t} else {\n\n\t\t\tconsole.warn( 'WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.' );\n\t\t\tthis.enableZoom = false;\n\n\t\t}\n\n\t}\n\n\t_dollyIn( dollyScale ) {\n\n\t\tif ( this.object.isPerspectiveCamera || this.object.isOrthographicCamera ) {\n\n\t\t\tthis._scale *= dollyScale;\n\n\t\t} else {\n\n\t\t\tconsole.warn( 'WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.' );\n\t\t\tthis.enableZoom = false;\n\n\t\t}\n\n\t}\n\n\t_updateZoomParameters( x, y ) {\n\n\t\tif ( ! this.zoomToCursor ) {\n\n\t\t\treturn;\n\n\t\t}\n\n\t\tthis._performCursorZoom = true;\n\n\t\tconst rect = this.domElement.getBoundingClientRect();\n\t\tconst dx = x - rect.left;\n\t\tconst dy = y - rect.top;\n\t\tconst w = rect.width;\n\t\tconst h = rect.height;\n\n\t\tthis._mouse.x = ( dx / w ) * 2 - 1;\n\t\tthis._mouse.y = - ( dy / h ) * 2 + 1;\n\n\t\tthis._dollyDirection.set( this._mouse.x, this._mouse.y, 1 ).unproject( this.object ).sub( this.object.position ).normalize();\n\n\t}\n\n\t_clampDistance( dist ) {\n\n\t\treturn Math.max( this.minDistance, Math.min( this.maxDistance, dist ) );\n\n\t}\n\n\t//\n\t// event callbacks - update the object state\n\t//\n\n\t_handleMouseDownRotate( event ) {\n\n\t\tthis._rotateStart.set( event.clientX, event.clientY );\n\n\t}\n\n\t_handleMouseDownDolly( event ) {\n\n\t\tthis._updateZoomParameters( event.clientX, event.clientX );\n\t\tthis._dollyStart.set( event.clientX, event.clientY );\n\n\t}\n\n\t_handleMouseDownPan( event ) {\n\n\t\tthis._panStart.set( event.clientX, event.clientY );\n\n\t}\n\n\t_handleMouseMoveRotate( event ) {\n\n\t\tthis._rotateEnd.set( event.clientX, event.clientY );\n\n\t\tthis._rotateDelta.subVectors( this._rotateEnd, this._rotateStart ).multiplyScalar( this.rotateSpeed );\n\n\t\tconst element = this.domElement;\n\n\t\tthis._rotateLeft( _twoPI * this._rotateDelta.x / element.clientHeight ); // yes, height\n\n\t\tthis._rotateUp( _twoPI * this._rotateDelta.y / element.clientHeight );\n\n\t\tthis._rotateStart.copy( this._rotateEnd );\n\n\t\tthis.update();\n\n\t}\n\n\t_handleMouseMoveDolly( event ) {\n\n\t\tthis._dollyEnd.set( event.clientX, event.clientY );\n\n\t\tthis._dollyDelta.subVectors( this._dollyEnd, this._dollyStart );\n\n\t\tif ( this._dollyDelta.y > 0 ) {\n\n\t\t\tthis._dollyOut( this._getZoomScale( this._dollyDelta.y ) );\n\n\t\t} else if ( this._dollyDelta.y < 0 ) {\n\n\t\t\tthis._dollyIn( this._getZoomScale( this._dollyDelta.y ) );\n\n\t\t}\n\n\t\tthis._dollyStart.copy( this._dollyEnd );\n\n\t\tthis.update();\n\n\t}\n\n\t_handleMouseMovePan( event ) {\n\n\t\tthis._panEnd.set( event.clientX, event.clientY );\n\n\t\tthis._panDelta.subVectors( this._panEnd, this._panStart ).multiplyScalar( this.panSpeed );\n\n\t\tthis._pan( this._panDelta.x, this._panDelta.y );\n\n\t\tthis._panStart.copy( this._panEnd );\n\n\t\tthis.update();\n\n\t}\n\n\t_handleMouseWheel( event ) {\n\n\t\tthis._updateZoomParameters( event.clientX, event.clientY );\n\n\t\tif ( event.deltaY < 0 ) {\n\n\t\t\tthis._dollyIn( this._getZoomScale( event.deltaY ) );\n\n\t\t} else if ( event.deltaY > 0 ) {\n\n\t\t\tthis._dollyOut( this._getZoomScale( event.deltaY ) );\n\n\t\t}\n\n\t\tthis.update();\n\n\t}\n\n\t_handleKeyDown( event ) {\n\n\t\tlet needsUpdate = false;\n\n\t\tswitch ( event.code ) {\n\n\t\t\tcase this.keys.UP:\n\n\t\t\t\tif ( event.ctrlKey || event.metaKey || event.shiftKey ) {\n\n\t\t\t\t\tif ( this.enableRotate ) {\n\n\t\t\t\t\t\tthis._rotateUp( _twoPI * this.keyRotateSpeed / this.domElement.clientHeight );\n\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\n\t\t\t\t\tif ( this.enablePan ) {\n\n\t\t\t\t\t\tthis._pan( 0, this.keyPanSpeed );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tneedsUpdate = true;\n\t\t\t\tbreak;\n\n\t\t\tcase this.keys.BOTTOM:\n\n\t\t\t\tif ( event.ctrlKey || event.metaKey || event.shiftKey ) {\n\n\t\t\t\t\tif ( this.enableRotate ) {\n\n\t\t\t\t\t\tthis._rotateUp( - _twoPI * this.keyRotateSpeed / this.domElement.clientHeight );\n\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\n\t\t\t\t\tif ( this.enablePan ) {\n\n\t\t\t\t\t\tthis._pan( 0, - this.keyPanSpeed );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tneedsUpdate = true;\n\t\t\t\tbreak;\n\n\t\t\tcase this.keys.LEFT:\n\n\t\t\t\tif ( event.ctrlKey || event.metaKey || event.shiftKey ) {\n\n\t\t\t\t\tif ( this.enableRotate ) {\n\n\t\t\t\t\t\tthis._rotateLeft( _twoPI * this.keyRotateSpeed / this.domElement.clientHeight );\n\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\n\t\t\t\t\tif ( this.enablePan ) {\n\n\t\t\t\t\t\tthis._pan( this.keyPanSpeed, 0 );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tneedsUpdate = true;\n\t\t\t\tbreak;\n\n\t\t\tcase this.keys.RIGHT:\n\n\t\t\t\tif ( event.ctrlKey || event.metaKey || event.shiftKey ) {\n\n\t\t\t\t\tif ( this.enableRotate ) {\n\n\t\t\t\t\t\tthis._rotateLeft( - _twoPI * this.keyRotateSpeed / this.domElement.clientHeight );\n\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\n\t\t\t\t\tif ( this.enablePan ) {\n\n\t\t\t\t\t\tthis._pan( - this.keyPanSpeed, 0 );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tneedsUpdate = true;\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\tif ( needsUpdate ) {\n\n\t\t\t// prevent the browser from scrolling on cursor keys\n\t\t\tevent.preventDefault();\n\n\t\t\tthis.update();\n\n\t\t}\n\n\n\t}\n\n\t_handleTouchStartRotate( event ) {\n\n\t\tif ( this._pointers.length === 1 ) {\n\n\t\t\tthis._rotateStart.set( event.pageX, event.pageY );\n\n\t\t} else {\n\n\t\t\tconst position = this._getSecondPointerPosition( event );\n\n\t\t\tconst x = 0.5 * ( event.pageX + position.x );\n\t\t\tconst y = 0.5 * ( event.pageY + position.y );\n\n\t\t\tthis._rotateStart.set( x, y );\n\n\t\t}\n\n\t}\n\n\t_handleTouchStartPan( event ) {\n\n\t\tif ( this._pointers.length === 1 ) {\n\n\t\t\tthis._panStart.set( event.pageX, event.pageY );\n\n\t\t} else {\n\n\t\t\tconst position = this._getSecondPointerPosition( event );\n\n\t\t\tconst x = 0.5 * ( event.pageX + position.x );\n\t\t\tconst y = 0.5 * ( event.pageY + position.y );\n\n\t\t\tthis._panStart.set( x, y );\n\n\t\t}\n\n\t}\n\n\t_handleTouchStartDolly( event ) {\n\n\t\tconst position = this._getSecondPointerPosition( event );\n\n\t\tconst dx = event.pageX - position.x;\n\t\tconst dy = event.pageY - position.y;\n\n\t\tconst distance = Math.sqrt( dx * dx + dy * dy );\n\n\t\tthis._dollyStart.set( 0, distance );\n\n\t}\n\n\t_handleTouchStartDollyPan( event ) {\n\n\t\tif ( this.enableZoom ) this._handleTouchStartDolly( event );\n\n\t\tif ( this.enablePan ) this._handleTouchStartPan( event );\n\n\t}\n\n\t_handleTouchStartDollyRotate( event ) {\n\n\t\tif ( this.enableZoom ) this._handleTouchStartDolly( event );\n\n\t\tif ( this.enableRotate ) this._handleTouchStartRotate( event );\n\n\t}\n\n\t_handleTouchMoveRotate( event ) {\n\n\t\tif ( this._pointers.length == 1 ) {\n\n\t\t\tthis._rotateEnd.set( event.pageX, event.pageY );\n\n\t\t} else {\n\n\t\t\tconst position = this._getSecondPointerPosition( event );\n\n\t\t\tconst x = 0.5 * ( event.pageX + position.x );\n\t\t\tconst y = 0.5 * ( event.pageY + position.y );\n\n\t\t\tthis._rotateEnd.set( x, y );\n\n\t\t}\n\n\t\tthis._rotateDelta.subVectors( this._rotateEnd, this._rotateStart ).multiplyScalar( this.rotateSpeed );\n\n\t\tconst element = this.domElement;\n\n\t\tthis._rotateLeft( _twoPI * this._rotateDelta.x / element.clientHeight ); // yes, height\n\n\t\tthis._rotateUp( _twoPI * this._rotateDelta.y / element.clientHeight );\n\n\t\tthis._rotateStart.copy( this._rotateEnd );\n\n\t}\n\n\t_handleTouchMovePan( event ) {\n\n\t\tif ( this._pointers.length === 1 ) {\n\n\t\t\tthis._panEnd.set( event.pageX, event.pageY );\n\n\t\t} else {\n\n\t\t\tconst position = this._getSecondPointerPosition( event );\n\n\t\t\tconst x = 0.5 * ( event.pageX + position.x );\n\t\t\tconst y = 0.5 * ( event.pageY + position.y );\n\n\t\t\tthis._panEnd.set( x, y );\n\n\t\t}\n\n\t\tthis._panDelta.subVectors( this._panEnd, this._panStart ).multiplyScalar( this.panSpeed );\n\n\t\tthis._pan( this._panDelta.x, this._panDelta.y );\n\n\t\tthis._panStart.copy( this._panEnd );\n\n\t}\n\n\t_handleTouchMoveDolly( event ) {\n\n\t\tconst position = this._getSecondPointerPosition( event );\n\n\t\tconst dx = event.pageX - position.x;\n\t\tconst dy = event.pageY - position.y;\n\n\t\tconst distance = Math.sqrt( dx * dx + dy * dy );\n\n\t\tthis._dollyEnd.set( 0, distance );\n\n\t\tthis._dollyDelta.set( 0, Math.pow( this._dollyEnd.y / this._dollyStart.y, this.zoomSpeed ) );\n\n\t\tthis._dollyOut( this._dollyDelta.y );\n\n\t\tthis._dollyStart.copy( this._dollyEnd );\n\n\t\tconst centerX = ( event.pageX + position.x ) * 0.5;\n\t\tconst centerY = ( event.pageY + position.y ) * 0.5;\n\n\t\tthis._updateZoomParameters( centerX, centerY );\n\n\t}\n\n\t_handleTouchMoveDollyPan( event ) {\n\n\t\tif ( this.enableZoom ) this._handleTouchMoveDolly( event );\n\n\t\tif ( this.enablePan ) this._handleTouchMovePan( event );\n\n\t}\n\n\t_handleTouchMoveDollyRotate( event ) {\n\n\t\tif ( this.enableZoom ) this._handleTouchMoveDolly( event );\n\n\t\tif ( this.enableRotate ) this._handleTouchMoveRotate( event );\n\n\t}\n\n\t// pointers\n\n\t_addPointer( event ) {\n\n\t\tthis._pointers.push( event.pointerId );\n\n\t}\n\n\t_removePointer( event ) {\n\n\t\tdelete this._pointerPositions[ event.pointerId ];\n\n\t\tfor ( let i = 0; i < this._pointers.length; i ++ ) {\n\n\t\t\tif ( this._pointers[ i ] == event.pointerId ) {\n\n\t\t\t\tthis._pointers.splice( i, 1 );\n\t\t\t\treturn;\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t_isTrackingPointer( event ) {\n\n\t\tfor ( let i = 0; i < this._pointers.length; i ++ ) {\n\n\t\t\tif ( this._pointers[ i ] == event.pointerId ) return true;\n\n\t\t}\n\n\t\treturn false;\n\n\t}\n\n\t_trackPointer( event ) {\n\n\t\tlet position = this._pointerPositions[ event.pointerId ];\n\n\t\tif ( position === undefined ) {\n\n\t\t\tposition = new Vector2();\n\t\t\tthis._pointerPositions[ event.pointerId ] = position;\n\n\t\t}\n\n\t\tposition.set( event.pageX, event.pageY );\n\n\t}\n\n\t_getSecondPointerPosition( event ) {\n\n\t\tconst pointerId = ( event.pointerId === this._pointers[ 0 ] ) ? this._pointers[ 1 ] : this._pointers[ 0 ];\n\n\t\treturn this._pointerPositions[ pointerId ];\n\n\t}\n\n\t//\n\n\t_customWheelEvent( event ) {\n\n\t\tconst mode = event.deltaMode;\n\n\t\t// minimal wheel event altered to meet delta-zoom demand\n\t\tconst newEvent = {\n\t\t\tclientX: event.clientX,\n\t\t\tclientY: event.clientY,\n\t\t\tdeltaY: event.deltaY,\n\t\t};\n\n\t\tswitch ( mode ) {\n\n\t\t\tcase 1: // LINE_MODE\n\t\t\t\tnewEvent.deltaY *= 16;\n\t\t\t\tbreak;\n\n\t\t\tcase 2: // PAGE_MODE\n\t\t\t\tnewEvent.deltaY *= 100;\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\t// detect if event was triggered by pinching\n\t\tif ( event.ctrlKey && ! this._controlActive ) {\n\n\t\t\tnewEvent.deltaY *= 10;\n\n\t\t}\n\n\t\treturn newEvent;\n\n\t}\n\n}\n\nfunction onPointerDown( event ) {\n\n\tif ( this.enabled === false ) return;\n\n\tif ( this._pointers.length === 0 ) {\n\n\t\tthis.domElement.setPointerCapture( event.pointerId );\n\n\t\tthis.domElement.addEventListener( 'pointermove', this._onPointerMove );\n\t\tthis.domElement.addEventListener( 'pointerup', this._onPointerUp );\n\n\t}\n\n\t//\n\n\tif ( this._isTrackingPointer( event ) ) return;\n\n\t//\n\n\tthis._addPointer( event );\n\n\tif ( event.pointerType === 'touch' ) {\n\n\t\tthis._onTouchStart( event );\n\n\t} else {\n\n\t\tthis._onMouseDown( event );\n\n\t}\n\n}\n\nfunction onPointerMove( event ) {\n\n\tif ( this.enabled === false ) return;\n\n\tif ( event.pointerType === 'touch' ) {\n\n\t\tthis._onTouchMove( event );\n\n\t} else {\n\n\t\tthis._onMouseMove( event );\n\n\t}\n\n}\n\nfunction onPointerUp( event ) {\n\n\tthis._removePointer( event );\n\n\tswitch ( this._pointers.length ) {\n\n\t\tcase 0:\n\n\t\t\tthis.domElement.releasePointerCapture( event.pointerId );\n\n\t\t\tthis.domElement.removeEventListener( 'pointermove', this._onPointerMove );\n\t\t\tthis.domElement.removeEventListener( 'pointerup', this._onPointerUp );\n\n\t\t\tthis.dispatchEvent( _endEvent );\n\n\t\t\tthis.state = _STATE.NONE;\n\n\t\t\tbreak;\n\n\t\tcase 1:\n\n\t\t\tconst pointerId = this._pointers[ 0 ];\n\t\t\tconst position = this._pointerPositions[ pointerId ];\n\n\t\t\t// minimal placeholder event - allows state correction on pointer-up\n\t\t\tthis._onTouchStart( { pointerId: pointerId, pageX: position.x, pageY: position.y } );\n\n\t\t\tbreak;\n\n\t}\n\n}\n\nfunction onMouseDown( event ) {\n\n\tlet mouseAction;\n\n\tswitch ( event.button ) {\n\n\t\tcase 0:\n\n\t\t\tmouseAction = this.mouseButtons.LEFT;\n\t\t\tbreak;\n\n\t\tcase 1:\n\n\t\t\tmouseAction = this.mouseButtons.MIDDLE;\n\t\t\tbreak;\n\n\t\tcase 2:\n\n\t\t\tmouseAction = this.mouseButtons.RIGHT;\n\t\t\tbreak;\n\n\t\tdefault:\n\n\t\t\tmouseAction = - 1;\n\n\t}\n\n\tswitch ( mouseAction ) {\n\n\t\tcase MOUSE.DOLLY:\n\n\t\t\tif ( this.enableZoom === false ) return;\n\n\t\t\tthis._handleMouseDownDolly( event );\n\n\t\t\tthis.state = _STATE.DOLLY;\n\n\t\t\tbreak;\n\n\t\tcase MOUSE.ROTATE:\n\n\t\t\tif ( event.ctrlKey || event.metaKey || event.shiftKey ) {\n\n\t\t\t\tif ( this.enablePan === false ) return;\n\n\t\t\t\tthis._handleMouseDownPan( event );\n\n\t\t\t\tthis.state = _STATE.PAN;\n\n\t\t\t} else {\n\n\t\t\t\tif ( this.enableRotate === false ) return;\n\n\t\t\t\tthis._handleMouseDownRotate( event );\n\n\t\t\t\tthis.state = _STATE.ROTATE;\n\n\t\t\t}\n\n\t\t\tbreak;\n\n\t\tcase MOUSE.PAN:\n\n\t\t\tif ( event.ctrlKey || event.metaKey || event.shiftKey ) {\n\n\t\t\t\tif ( this.enableRotate === false ) return;\n\n\t\t\t\tthis._handleMouseDownRotate( event );\n\n\t\t\t\tthis.state = _STATE.ROTATE;\n\n\t\t\t} else {\n\n\t\t\t\tif ( this.enablePan === false ) return;\n\n\t\t\t\tthis._handleMouseDownPan( event );\n\n\t\t\t\tthis.state = _STATE.PAN;\n\n\t\t\t}\n\n\t\t\tbreak;\n\n\t\tdefault:\n\n\t\t\tthis.state = _STATE.NONE;\n\n\t}\n\n\tif ( this.state !== _STATE.NONE ) {\n\n\t\tthis.dispatchEvent( _startEvent );\n\n\t}\n\n}\n\nfunction onMouseMove( event ) {\n\n\tswitch ( this.state ) {\n\n\t\tcase _STATE.ROTATE:\n\n\t\t\tif ( this.enableRotate === false ) return;\n\n\t\t\tthis._handleMouseMoveRotate( event );\n\n\t\t\tbreak;\n\n\t\tcase _STATE.DOLLY:\n\n\t\t\tif ( this.enableZoom === false ) return;\n\n\t\t\tthis._handleMouseMoveDolly( event );\n\n\t\t\tbreak;\n\n\t\tcase _STATE.PAN:\n\n\t\t\tif ( this.enablePan === false ) return;\n\n\t\t\tthis._handleMouseMovePan( event );\n\n\t\t\tbreak;\n\n\t}\n\n}\n\nfunction onMouseWheel( event ) {\n\n\tif ( this.enabled === false || this.enableZoom === false || this.state !== _STATE.NONE ) return;\n\n\tevent.preventDefault();\n\n\tthis.dispatchEvent( _startEvent );\n\n\tthis._handleMouseWheel( this._customWheelEvent( event ) );\n\n\tthis.dispatchEvent( _endEvent );\n\n}\n\nfunction onKeyDown( event ) {\n\n\tif ( this.enabled === false ) return;\n\n\tthis._handleKeyDown( event );\n\n}\n\nfunction onTouchStart( event ) {\n\n\tthis._trackPointer( event );\n\n\tswitch ( this._pointers.length ) {\n\n\t\tcase 1:\n\n\t\t\tswitch ( this.touches.ONE ) {\n\n\t\t\t\tcase TOUCH.ROTATE:\n\n\t\t\t\t\tif ( this.enableRotate === false ) return;\n\n\t\t\t\t\tthis._handleTouchStartRotate( event );\n\n\t\t\t\t\tthis.state = _STATE.TOUCH_ROTATE;\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase TOUCH.PAN:\n\n\t\t\t\t\tif ( this.enablePan === false ) return;\n\n\t\t\t\t\tthis._handleTouchStartPan( event );\n\n\t\t\t\t\tthis.state = _STATE.TOUCH_PAN;\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\n\t\t\t\t\tthis.state = _STATE.NONE;\n\n\t\t\t}\n\n\t\t\tbreak;\n\n\t\tcase 2:\n\n\t\t\tswitch ( this.touches.TWO ) {\n\n\t\t\t\tcase TOUCH.DOLLY_PAN:\n\n\t\t\t\t\tif ( this.enableZoom === false && this.enablePan === false ) return;\n\n\t\t\t\t\tthis._handleTouchStartDollyPan( event );\n\n\t\t\t\t\tthis.state = _STATE.TOUCH_DOLLY_PAN;\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase TOUCH.DOLLY_ROTATE:\n\n\t\t\t\t\tif ( this.enableZoom === false && this.enableRotate === false ) return;\n\n\t\t\t\t\tthis._handleTouchStartDollyRotate( event );\n\n\t\t\t\t\tthis.state = _STATE.TOUCH_DOLLY_ROTATE;\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\n\t\t\t\t\tthis.state = _STATE.NONE;\n\n\t\t\t}\n\n\t\t\tbreak;\n\n\t\tdefault:\n\n\t\t\tthis.state = _STATE.NONE;\n\n\t}\n\n\tif ( this.state !== _STATE.NONE ) {\n\n\t\tthis.dispatchEvent( _startEvent );\n\n\t}\n\n}\n\nfunction onTouchMove( event ) {\n\n\tthis._trackPointer( event );\n\n\tswitch ( this.state ) {\n\n\t\tcase _STATE.TOUCH_ROTATE:\n\n\t\t\tif ( this.enableRotate === false ) return;\n\n\t\t\tthis._handleTouchMoveRotate( event );\n\n\t\t\tthis.update();\n\n\t\t\tbreak;\n\n\t\tcase _STATE.TOUCH_PAN:\n\n\t\t\tif ( this.enablePan === false ) return;\n\n\t\t\tthis._handleTouchMovePan( event );\n\n\t\t\tthis.update();\n\n\t\t\tbreak;\n\n\t\tcase _STATE.TOUCH_DOLLY_PAN:\n\n\t\t\tif ( this.enableZoom === false && this.enablePan === false ) return;\n\n\t\t\tthis._handleTouchMoveDollyPan( event );\n\n\t\t\tthis.update();\n\n\t\t\tbreak;\n\n\t\tcase _STATE.TOUCH_DOLLY_ROTATE:\n\n\t\t\tif ( this.enableZoom === false && this.enableRotate === false ) return;\n\n\t\t\tthis._handleTouchMoveDollyRotate( event );\n\n\t\t\tthis.update();\n\n\t\t\tbreak;\n\n\t\tdefault:\n\n\t\t\tthis.state = _STATE.NONE;\n\n\t}\n\n}\n\nfunction onContextMenu( event ) {\n\n\tif ( this.enabled === false ) return;\n\n\tevent.preventDefault();\n\n}\n\nfunction interceptControlDown( event ) {\n\n\tif ( event.key === 'Control' ) {\n\n\t\tthis._controlActive = true;\n\n\t\tconst document = this.domElement.getRootNode(); // offscreen canvas compatibility\n\n\t\tdocument.addEventListener( 'keyup', this._interceptControlUp, { passive: true, capture: true } );\n\n\t}\n\n}\n\nfunction interceptControlUp( event ) {\n\n\tif ( event.key === 'Control' ) {\n\n\t\tthis._controlActive = false;\n\n\t\tconst document = this.domElement.getRootNode(); // offscreen canvas compatibility\n\n\t\tdocument.removeEventListener( 'keyup', this._interceptControlUp, { passive: true, capture: true } );\n\n\t}\n\n}\n\nexport { OrbitControls };\n"], "mappings": ";;;;;;;;;;;;;;AAmBA,IAAM,eAAe,EAAE,MAAM,SAAS;AAQtC,IAAM,cAAc,EAAE,MAAM,QAAQ;AAQpC,IAAM,YAAY,EAAE,MAAM,MAAM;AAEhC,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,SAAS,IAAI,MAAM;AACzB,IAAM,cAAc,KAAK,IAAK,KAAK,UAAU,OAAQ;AAErD,IAAM,KAAK,IAAI,QAAQ;AACvB,IAAM,SAAS,IAAI,KAAK;AAExB,IAAM,SAAS;AAAA,EACd,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,KAAK;AAAA,EACL,cAAc;AAAA,EACd,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,oBAAoB;AACrB;AACA,IAAM,OAAO;AAiCb,IAAM,gBAAN,cAA4B,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQpC,YAAa,QAAQ,aAAa,MAAO;AAExC,UAAO,QAAQ,UAAW;AAE1B,SAAK,QAAQ,OAAO;AAQpB,SAAK,SAAS,IAAI,QAAQ;AAS1B,SAAK,SAAS,IAAI,QAAQ;AAQ1B,SAAK,cAAc;AAQnB,SAAK,cAAc;AAQnB,SAAK,UAAU;AAQf,SAAK,UAAU;AAQf,SAAK,kBAAkB;AAQvB,SAAK,kBAAkB;AAQvB,SAAK,gBAAgB;AAQrB,SAAK,gBAAgB,KAAK;AAS1B,SAAK,kBAAkB;AASvB,SAAK,kBAAkB;AAUvB,SAAK,gBAAgB;AAUrB,SAAK,gBAAgB;AAQrB,SAAK,aAAa;AAQlB,SAAK,YAAY;AAYjB,SAAK,eAAe;AAQpB,SAAK,cAAc;AAQnB,SAAK,iBAAiB;AAQtB,SAAK,YAAY;AAQjB,SAAK,WAAW;AAUhB,SAAK,qBAAqB;AAS1B,SAAK,cAAc;AAQnB,SAAK,eAAe;AAYpB,SAAK,aAAa;AAWlB,SAAK,kBAAkB;AAevB,SAAK,OAAO,EAAE,MAAM,aAAa,IAAI,WAAW,OAAO,cAAc,QAAQ,YAAY;AAczF,SAAK,eAAe,EAAE,MAAM,MAAM,QAAQ,QAAQ,MAAM,OAAO,OAAO,MAAM,IAAI;AAahF,SAAK,UAAU,EAAE,KAAK,MAAM,QAAQ,KAAK,MAAM,UAAU;AAOzD,SAAK,UAAU,KAAK,OAAO,MAAM;AAOjC,SAAK,YAAY,KAAK,OAAO,SAAS,MAAM;AAO5C,SAAK,QAAQ,KAAK,OAAO;AAGzB,SAAK,uBAAuB;AAI5B,SAAK,gBAAgB,IAAI,QAAQ;AACjC,SAAK,kBAAkB,IAAI,WAAW;AACtC,SAAK,sBAAsB,IAAI,QAAQ;AAGvC,SAAK,QAAQ,IAAI,WAAW,EAAE,mBAAoB,OAAO,IAAI,IAAI,QAAS,GAAG,GAAG,CAAE,CAAE;AACpF,SAAK,eAAe,KAAK,MAAM,MAAM,EAAE,OAAO;AAG9C,SAAK,aAAa,IAAI,UAAU;AAChC,SAAK,kBAAkB,IAAI,UAAU;AAErC,SAAK,SAAS;AACd,SAAK,aAAa,IAAI,QAAQ;AAE9B,SAAK,eAAe,IAAI,QAAQ;AAChC,SAAK,aAAa,IAAI,QAAQ;AAC9B,SAAK,eAAe,IAAI,QAAQ;AAEhC,SAAK,YAAY,IAAI,QAAQ;AAC7B,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,YAAY,IAAI,QAAQ;AAE7B,SAAK,cAAc,IAAI,QAAQ;AAC/B,SAAK,YAAY,IAAI,QAAQ;AAC7B,SAAK,cAAc,IAAI,QAAQ;AAE/B,SAAK,kBAAkB,IAAI,QAAQ;AACnC,SAAK,SAAS,IAAI,QAAQ;AAC1B,SAAK,qBAAqB;AAE1B,SAAK,YAAY,CAAC;AAClB,SAAK,oBAAoB,CAAC;AAE1B,SAAK,iBAAiB;AAItB,SAAK,iBAAiB,cAAc,KAAM,IAAK;AAC/C,SAAK,iBAAiB,cAAc,KAAM,IAAK;AAC/C,SAAK,eAAe,YAAY,KAAM,IAAK;AAC3C,SAAK,iBAAiB,cAAc,KAAM,IAAK;AAC/C,SAAK,gBAAgB,aAAa,KAAM,IAAK;AAC7C,SAAK,aAAa,UAAU,KAAM,IAAK;AAEvC,SAAK,gBAAgB,aAAa,KAAM,IAAK;AAC7C,SAAK,eAAe,YAAY,KAAM,IAAK;AAE3C,SAAK,eAAe,YAAY,KAAM,IAAK;AAC3C,SAAK,eAAe,YAAY,KAAM,IAAK;AAE3C,SAAK,wBAAwB,qBAAqB,KAAM,IAAK;AAC7D,SAAK,sBAAsB,mBAAmB,KAAM,IAAK;AAIzD,QAAK,KAAK,eAAe,MAAO;AAE/B,WAAK,QAAS,KAAK,UAAW;AAAA,IAE/B;AAEA,SAAK,OAAO;AAAA,EAEb;AAAA,EAEA,QAAS,SAAU;AAElB,UAAM,QAAS,OAAQ;AAEvB,SAAK,WAAW,iBAAkB,eAAe,KAAK,cAAe;AACrE,SAAK,WAAW,iBAAkB,iBAAiB,KAAK,YAAa;AAErE,SAAK,WAAW,iBAAkB,eAAe,KAAK,cAAe;AACrE,SAAK,WAAW,iBAAkB,SAAS,KAAK,eAAe,EAAE,SAAS,MAAM,CAAE;AAElF,UAAM,WAAW,KAAK,WAAW,YAAY;AAC7C,aAAS,iBAAkB,WAAW,KAAK,uBAAuB,EAAE,SAAS,MAAM,SAAS,KAAK,CAAE;AAEnG,SAAK,WAAW,MAAM,cAAc;AAAA,EAErC;AAAA,EAEA,aAAa;AAEZ,SAAK,WAAW,oBAAqB,eAAe,KAAK,cAAe;AACxE,SAAK,WAAW,oBAAqB,eAAe,KAAK,cAAe;AACxE,SAAK,WAAW,oBAAqB,aAAa,KAAK,YAAa;AACpE,SAAK,WAAW,oBAAqB,iBAAiB,KAAK,YAAa;AAExE,SAAK,WAAW,oBAAqB,SAAS,KAAK,aAAc;AACjE,SAAK,WAAW,oBAAqB,eAAe,KAAK,cAAe;AAExE,SAAK,sBAAsB;AAE3B,UAAM,WAAW,KAAK,WAAW,YAAY;AAC7C,aAAS,oBAAqB,WAAW,KAAK,uBAAuB,EAAE,SAAS,KAAK,CAAE;AAEvF,SAAK,WAAW,MAAM,cAAc;AAAA,EAErC;AAAA,EAEA,UAAU;AAET,SAAK,WAAW;AAAA,EAEjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AAEf,WAAO,KAAK,WAAW;AAAA,EAExB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB;AAEnB,WAAO,KAAK,WAAW;AAAA,EAExB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AAEb,WAAO,KAAK,OAAO,SAAS,WAAY,KAAK,MAAO;AAAA,EAErD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAmB,YAAa;AAE/B,eAAW,iBAAkB,WAAW,KAAK,UAAW;AACxD,SAAK,uBAAuB;AAAA,EAE7B;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AAEvB,QAAK,KAAK,yBAAyB,MAAO;AAEzC,WAAK,qBAAqB,oBAAqB,WAAW,KAAK,UAAW;AAC1E,WAAK,uBAAuB;AAAA,IAE7B;AAAA,EAED;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AAEX,SAAK,QAAQ,KAAM,KAAK,MAAO;AAC/B,SAAK,UAAU,KAAM,KAAK,OAAO,QAAS;AAC1C,SAAK,QAAQ,KAAK,OAAO;AAAA,EAE1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AAEP,SAAK,OAAO,KAAM,KAAK,OAAQ;AAC/B,SAAK,OAAO,SAAS,KAAM,KAAK,SAAU;AAC1C,SAAK,OAAO,OAAO,KAAK;AAExB,SAAK,OAAO,uBAAuB;AACnC,SAAK,cAAe,YAAa;AAEjC,SAAK,OAAO;AAEZ,SAAK,QAAQ,OAAO;AAAA,EAErB;AAAA,EAEA,OAAQ,YAAY,MAAO;AAE1B,UAAM,WAAW,KAAK,OAAO;AAE7B,OAAG,KAAM,QAAS,EAAE,IAAK,KAAK,MAAO;AAGrC,OAAG,gBAAiB,KAAK,KAAM;AAG/B,SAAK,WAAW,eAAgB,EAAG;AAEnC,QAAK,KAAK,cAAc,KAAK,UAAU,OAAO,MAAO;AAEpD,WAAK,YAAa,KAAK,sBAAuB,SAAU,CAAE;AAAA,IAE3D;AAEA,QAAK,KAAK,eAAgB;AAEzB,WAAK,WAAW,SAAS,KAAK,gBAAgB,QAAQ,KAAK;AAC3D,WAAK,WAAW,OAAO,KAAK,gBAAgB,MAAM,KAAK;AAAA,IAExD,OAAO;AAEN,WAAK,WAAW,SAAS,KAAK,gBAAgB;AAC9C,WAAK,WAAW,OAAO,KAAK,gBAAgB;AAAA,IAE7C;AAIA,QAAI,MAAM,KAAK;AACf,QAAI,MAAM,KAAK;AAEf,QAAK,SAAU,GAAI,KAAK,SAAU,GAAI,GAAI;AAEzC,UAAK,MAAM,CAAE,KAAK,GAAK,QAAO;AAAA,eAAkB,MAAM,KAAK,GAAK,QAAO;AAEvE,UAAK,MAAM,CAAE,KAAK,GAAK,QAAO;AAAA,eAAkB,MAAM,KAAK,GAAK,QAAO;AAEvE,UAAK,OAAO,KAAM;AAEjB,aAAK,WAAW,QAAQ,KAAK,IAAK,KAAK,KAAK,IAAK,KAAK,KAAK,WAAW,KAAM,CAAE;AAAA,MAE/E,OAAO;AAEN,aAAK,WAAW,QAAU,KAAK,WAAW,SAAU,MAAM,OAAQ,IACjE,KAAK,IAAK,KAAK,KAAK,WAAW,KAAM,IACrC,KAAK,IAAK,KAAK,KAAK,WAAW,KAAM;AAAA,MAEvC;AAAA,IAED;AAGA,SAAK,WAAW,MAAM,KAAK,IAAK,KAAK,eAAe,KAAK,IAAK,KAAK,eAAe,KAAK,WAAW,GAAI,CAAE;AAExG,SAAK,WAAW,SAAS;AAKzB,QAAK,KAAK,kBAAkB,MAAO;AAElC,WAAK,OAAO,gBAAiB,KAAK,YAAY,KAAK,aAAc;AAAA,IAElE,OAAO;AAEN,WAAK,OAAO,IAAK,KAAK,UAAW;AAAA,IAElC;AAGA,SAAK,OAAO,IAAK,KAAK,MAAO;AAC7B,SAAK,OAAO,YAAa,KAAK,iBAAiB,KAAK,eAAgB;AACpE,SAAK,OAAO,IAAK,KAAK,MAAO;AAE7B,QAAI,cAAc;AAGlB,QAAK,KAAK,gBAAgB,KAAK,sBAAsB,KAAK,OAAO,sBAAuB;AAEvF,WAAK,WAAW,SAAS,KAAK,eAAgB,KAAK,WAAW,MAAO;AAAA,IAEtE,OAAO;AAEN,YAAM,aAAa,KAAK,WAAW;AACnC,WAAK,WAAW,SAAS,KAAK,eAAgB,KAAK,WAAW,SAAS,KAAK,MAAO;AACnF,oBAAc,cAAc,KAAK,WAAW;AAAA,IAE7C;AAEA,OAAG,iBAAkB,KAAK,UAAW;AAGrC,OAAG,gBAAiB,KAAK,YAAa;AAEtC,aAAS,KAAM,KAAK,MAAO,EAAE,IAAK,EAAG;AAErC,SAAK,OAAO,OAAQ,KAAK,MAAO;AAEhC,QAAK,KAAK,kBAAkB,MAAO;AAElC,WAAK,gBAAgB,SAAW,IAAI,KAAK;AACzC,WAAK,gBAAgB,OAAS,IAAI,KAAK;AAEvC,WAAK,WAAW,eAAgB,IAAI,KAAK,aAAc;AAAA,IAExD,OAAO;AAEN,WAAK,gBAAgB,IAAK,GAAG,GAAG,CAAE;AAElC,WAAK,WAAW,IAAK,GAAG,GAAG,CAAE;AAAA,IAE9B;AAGA,QAAK,KAAK,gBAAgB,KAAK,oBAAqB;AAEnD,UAAI,YAAY;AAChB,UAAK,KAAK,OAAO,qBAAsB;AAItC,cAAM,aAAa,GAAG,OAAO;AAC7B,oBAAY,KAAK,eAAgB,aAAa,KAAK,MAAO;AAE1D,cAAM,cAAc,aAAa;AACjC,aAAK,OAAO,SAAS,gBAAiB,KAAK,iBAAiB,WAAY;AACxE,aAAK,OAAO,kBAAkB;AAE9B,sBAAc,CAAC,CAAE;AAAA,MAElB,WAAY,KAAK,OAAO,sBAAuB;AAG9C,cAAM,cAAc,IAAI,QAAS,KAAK,OAAO,GAAG,KAAK,OAAO,GAAG,CAAE;AACjE,oBAAY,UAAW,KAAK,MAAO;AAEnC,cAAM,WAAW,KAAK,OAAO;AAC7B,aAAK,OAAO,OAAO,KAAK,IAAK,KAAK,SAAS,KAAK,IAAK,KAAK,SAAS,KAAK,OAAO,OAAO,KAAK,MAAO,CAAE;AACpG,aAAK,OAAO,uBAAuB;AAEnC,sBAAc,aAAa,KAAK,OAAO;AAEvC,cAAM,aAAa,IAAI,QAAS,KAAK,OAAO,GAAG,KAAK,OAAO,GAAG,CAAE;AAChE,mBAAW,UAAW,KAAK,MAAO;AAElC,aAAK,OAAO,SAAS,IAAK,UAAW,EAAE,IAAK,WAAY;AACxD,aAAK,OAAO,kBAAkB;AAE9B,oBAAY,GAAG,OAAO;AAAA,MAEvB,OAAO;AAEN,gBAAQ,KAAM,yFAA0F;AACxG,aAAK,eAAe;AAAA,MAErB;AAGA,UAAK,cAAc,MAAO;AAEzB,YAAK,KAAK,oBAAqB;AAG9B,eAAK,OAAO,IAAK,GAAG,GAAG,EAAI,EACzB,mBAAoB,KAAK,OAAO,MAAO,EACvC,eAAgB,SAAU,EAC1B,IAAK,KAAK,OAAO,QAAS;AAAA,QAE7B,OAAO;AAGN,eAAK,OAAO,KAAM,KAAK,OAAO,QAAS;AACvC,eAAK,UAAU,IAAK,GAAG,GAAG,EAAI,EAAE,mBAAoB,KAAK,OAAO,MAAO;AAIvE,cAAK,KAAK,IAAK,KAAK,OAAO,GAAG,IAAK,KAAK,SAAU,CAAE,IAAI,aAAc;AAErE,iBAAK,OAAO,OAAQ,KAAK,MAAO;AAAA,UAEjC,OAAO;AAEN,mBAAO,8BAA+B,KAAK,OAAO,IAAI,KAAK,MAAO;AAClE,iBAAK,eAAgB,QAAQ,KAAK,MAAO;AAAA,UAE1C;AAAA,QAED;AAAA,MAED;AAAA,IAED,WAAY,KAAK,OAAO,sBAAuB;AAE9C,YAAM,WAAW,KAAK,OAAO;AAC7B,WAAK,OAAO,OAAO,KAAK,IAAK,KAAK,SAAS,KAAK,IAAK,KAAK,SAAS,KAAK,OAAO,OAAO,KAAK,MAAO,CAAE;AAEpG,UAAK,aAAa,KAAK,OAAO,MAAO;AAEpC,aAAK,OAAO,uBAAuB;AACnC,sBAAc;AAAA,MAEf;AAAA,IAED;AAEA,SAAK,SAAS;AACd,SAAK,qBAAqB;AAM1B,QAAK,eACJ,KAAK,cAAc,kBAAmB,KAAK,OAAO,QAAS,IAAI,QAC/D,KAAM,IAAI,KAAK,gBAAgB,IAAK,KAAK,OAAO,UAAW,KAAM,QACjE,KAAK,oBAAoB,kBAAmB,KAAK,MAAO,IAAI,MAAO;AAEnE,WAAK,cAAe,YAAa;AAEjC,WAAK,cAAc,KAAM,KAAK,OAAO,QAAS;AAC9C,WAAK,gBAAgB,KAAM,KAAK,OAAO,UAAW;AAClD,WAAK,oBAAoB,KAAM,KAAK,MAAO;AAE3C,aAAO;AAAA,IAER;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,sBAAuB,WAAY;AAElC,QAAK,cAAc,MAAO;AAEzB,aAAS,SAAS,KAAK,KAAK,kBAAoB;AAAA,IAEjD,OAAO;AAEN,aAAO,SAAS,KAAK,KAAK,KAAK;AAAA,IAEhC;AAAA,EAED;AAAA,EAEA,cAAe,OAAQ;AAEtB,UAAM,kBAAkB,KAAK,IAAK,QAAQ,IAAK;AAC/C,WAAO,KAAK,IAAK,MAAM,KAAK,YAAY,eAAgB;AAAA,EAEzD;AAAA,EAEA,YAAa,OAAQ;AAEpB,SAAK,gBAAgB,SAAS;AAAA,EAE/B;AAAA,EAEA,UAAW,OAAQ;AAElB,SAAK,gBAAgB,OAAO;AAAA,EAE7B;AAAA,EAEA,SAAU,UAAU,cAAe;AAElC,OAAG,oBAAqB,cAAc,CAAE;AACxC,OAAG,eAAgB,CAAE,QAAS;AAE9B,SAAK,WAAW,IAAK,EAAG;AAAA,EAEzB;AAAA,EAEA,OAAQ,UAAU,cAAe;AAEhC,QAAK,KAAK,uBAAuB,MAAO;AAEvC,SAAG,oBAAqB,cAAc,CAAE;AAAA,IAEzC,OAAO;AAEN,SAAG,oBAAqB,cAAc,CAAE;AACxC,SAAG,aAAc,KAAK,OAAO,IAAI,EAAG;AAAA,IAErC;AAEA,OAAG,eAAgB,QAAS;AAE5B,SAAK,WAAW,IAAK,EAAG;AAAA,EAEzB;AAAA;AAAA,EAGA,KAAM,QAAQ,QAAS;AAEtB,UAAM,UAAU,KAAK;AAErB,QAAK,KAAK,OAAO,qBAAsB;AAGtC,YAAM,WAAW,KAAK,OAAO;AAC7B,SAAG,KAAM,QAAS,EAAE,IAAK,KAAK,MAAO;AACrC,UAAI,iBAAiB,GAAG,OAAO;AAG/B,wBAAkB,KAAK,IAAO,KAAK,OAAO,MAAM,IAAM,KAAK,KAAK,GAAM;AAGtE,WAAK,SAAU,IAAI,SAAS,iBAAiB,QAAQ,cAAc,KAAK,OAAO,MAAO;AACtF,WAAK,OAAQ,IAAI,SAAS,iBAAiB,QAAQ,cAAc,KAAK,OAAO,MAAO;AAAA,IAErF,WAAY,KAAK,OAAO,sBAAuB;AAG9C,WAAK,SAAU,UAAW,KAAK,OAAO,QAAQ,KAAK,OAAO,QAAS,KAAK,OAAO,OAAO,QAAQ,aAAa,KAAK,OAAO,MAAO;AAC9H,WAAK,OAAQ,UAAW,KAAK,OAAO,MAAM,KAAK,OAAO,UAAW,KAAK,OAAO,OAAO,QAAQ,cAAc,KAAK,OAAO,MAAO;AAAA,IAE9H,OAAO;AAGN,cAAQ,KAAM,8EAA+E;AAC7F,WAAK,YAAY;AAAA,IAElB;AAAA,EAED;AAAA,EAEA,UAAW,YAAa;AAEvB,QAAK,KAAK,OAAO,uBAAuB,KAAK,OAAO,sBAAuB;AAE1E,WAAK,UAAU;AAAA,IAEhB,OAAO;AAEN,cAAQ,KAAM,qFAAsF;AACpG,WAAK,aAAa;AAAA,IAEnB;AAAA,EAED;AAAA,EAEA,SAAU,YAAa;AAEtB,QAAK,KAAK,OAAO,uBAAuB,KAAK,OAAO,sBAAuB;AAE1E,WAAK,UAAU;AAAA,IAEhB,OAAO;AAEN,cAAQ,KAAM,qFAAsF;AACpG,WAAK,aAAa;AAAA,IAEnB;AAAA,EAED;AAAA,EAEA,sBAAuB,GAAG,GAAI;AAE7B,QAAK,CAAE,KAAK,cAAe;AAE1B;AAAA,IAED;AAEA,SAAK,qBAAqB;AAE1B,UAAM,OAAO,KAAK,WAAW,sBAAsB;AACnD,UAAM,KAAK,IAAI,KAAK;AACpB,UAAM,KAAK,IAAI,KAAK;AACpB,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AAEf,SAAK,OAAO,IAAM,KAAK,IAAM,IAAI;AACjC,SAAK,OAAO,IAAI,EAAI,KAAK,KAAM,IAAI;AAEnC,SAAK,gBAAgB,IAAK,KAAK,OAAO,GAAG,KAAK,OAAO,GAAG,CAAE,EAAE,UAAW,KAAK,MAAO,EAAE,IAAK,KAAK,OAAO,QAAS,EAAE,UAAU;AAAA,EAE5H;AAAA,EAEA,eAAgB,MAAO;AAEtB,WAAO,KAAK,IAAK,KAAK,aAAa,KAAK,IAAK,KAAK,aAAa,IAAK,CAAE;AAAA,EAEvE;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAwB,OAAQ;AAE/B,SAAK,aAAa,IAAK,MAAM,SAAS,MAAM,OAAQ;AAAA,EAErD;AAAA,EAEA,sBAAuB,OAAQ;AAE9B,SAAK,sBAAuB,MAAM,SAAS,MAAM,OAAQ;AACzD,SAAK,YAAY,IAAK,MAAM,SAAS,MAAM,OAAQ;AAAA,EAEpD;AAAA,EAEA,oBAAqB,OAAQ;AAE5B,SAAK,UAAU,IAAK,MAAM,SAAS,MAAM,OAAQ;AAAA,EAElD;AAAA,EAEA,uBAAwB,OAAQ;AAE/B,SAAK,WAAW,IAAK,MAAM,SAAS,MAAM,OAAQ;AAElD,SAAK,aAAa,WAAY,KAAK,YAAY,KAAK,YAAa,EAAE,eAAgB,KAAK,WAAY;AAEpG,UAAM,UAAU,KAAK;AAErB,SAAK,YAAa,SAAS,KAAK,aAAa,IAAI,QAAQ,YAAa;AAEtE,SAAK,UAAW,SAAS,KAAK,aAAa,IAAI,QAAQ,YAAa;AAEpE,SAAK,aAAa,KAAM,KAAK,UAAW;AAExC,SAAK,OAAO;AAAA,EAEb;AAAA,EAEA,sBAAuB,OAAQ;AAE9B,SAAK,UAAU,IAAK,MAAM,SAAS,MAAM,OAAQ;AAEjD,SAAK,YAAY,WAAY,KAAK,WAAW,KAAK,WAAY;AAE9D,QAAK,KAAK,YAAY,IAAI,GAAI;AAE7B,WAAK,UAAW,KAAK,cAAe,KAAK,YAAY,CAAE,CAAE;AAAA,IAE1D,WAAY,KAAK,YAAY,IAAI,GAAI;AAEpC,WAAK,SAAU,KAAK,cAAe,KAAK,YAAY,CAAE,CAAE;AAAA,IAEzD;AAEA,SAAK,YAAY,KAAM,KAAK,SAAU;AAEtC,SAAK,OAAO;AAAA,EAEb;AAAA,EAEA,oBAAqB,OAAQ;AAE5B,SAAK,QAAQ,IAAK,MAAM,SAAS,MAAM,OAAQ;AAE/C,SAAK,UAAU,WAAY,KAAK,SAAS,KAAK,SAAU,EAAE,eAAgB,KAAK,QAAS;AAExF,SAAK,KAAM,KAAK,UAAU,GAAG,KAAK,UAAU,CAAE;AAE9C,SAAK,UAAU,KAAM,KAAK,OAAQ;AAElC,SAAK,OAAO;AAAA,EAEb;AAAA,EAEA,kBAAmB,OAAQ;AAE1B,SAAK,sBAAuB,MAAM,SAAS,MAAM,OAAQ;AAEzD,QAAK,MAAM,SAAS,GAAI;AAEvB,WAAK,SAAU,KAAK,cAAe,MAAM,MAAO,CAAE;AAAA,IAEnD,WAAY,MAAM,SAAS,GAAI;AAE9B,WAAK,UAAW,KAAK,cAAe,MAAM,MAAO,CAAE;AAAA,IAEpD;AAEA,SAAK,OAAO;AAAA,EAEb;AAAA,EAEA,eAAgB,OAAQ;AAEvB,QAAI,cAAc;AAElB,YAAS,MAAM,MAAO;AAAA,MAErB,KAAK,KAAK,KAAK;AAEd,YAAK,MAAM,WAAW,MAAM,WAAW,MAAM,UAAW;AAEvD,cAAK,KAAK,cAAe;AAExB,iBAAK,UAAW,SAAS,KAAK,iBAAiB,KAAK,WAAW,YAAa;AAAA,UAE7E;AAAA,QAED,OAAO;AAEN,cAAK,KAAK,WAAY;AAErB,iBAAK,KAAM,GAAG,KAAK,WAAY;AAAA,UAEhC;AAAA,QAED;AAEA,sBAAc;AACd;AAAA,MAED,KAAK,KAAK,KAAK;AAEd,YAAK,MAAM,WAAW,MAAM,WAAW,MAAM,UAAW;AAEvD,cAAK,KAAK,cAAe;AAExB,iBAAK,UAAW,CAAE,SAAS,KAAK,iBAAiB,KAAK,WAAW,YAAa;AAAA,UAE/E;AAAA,QAED,OAAO;AAEN,cAAK,KAAK,WAAY;AAErB,iBAAK,KAAM,GAAG,CAAE,KAAK,WAAY;AAAA,UAElC;AAAA,QAED;AAEA,sBAAc;AACd;AAAA,MAED,KAAK,KAAK,KAAK;AAEd,YAAK,MAAM,WAAW,MAAM,WAAW,MAAM,UAAW;AAEvD,cAAK,KAAK,cAAe;AAExB,iBAAK,YAAa,SAAS,KAAK,iBAAiB,KAAK,WAAW,YAAa;AAAA,UAE/E;AAAA,QAED,OAAO;AAEN,cAAK,KAAK,WAAY;AAErB,iBAAK,KAAM,KAAK,aAAa,CAAE;AAAA,UAEhC;AAAA,QAED;AAEA,sBAAc;AACd;AAAA,MAED,KAAK,KAAK,KAAK;AAEd,YAAK,MAAM,WAAW,MAAM,WAAW,MAAM,UAAW;AAEvD,cAAK,KAAK,cAAe;AAExB,iBAAK,YAAa,CAAE,SAAS,KAAK,iBAAiB,KAAK,WAAW,YAAa;AAAA,UAEjF;AAAA,QAED,OAAO;AAEN,cAAK,KAAK,WAAY;AAErB,iBAAK,KAAM,CAAE,KAAK,aAAa,CAAE;AAAA,UAElC;AAAA,QAED;AAEA,sBAAc;AACd;AAAA,IAEF;AAEA,QAAK,aAAc;AAGlB,YAAM,eAAe;AAErB,WAAK,OAAO;AAAA,IAEb;AAAA,EAGD;AAAA,EAEA,wBAAyB,OAAQ;AAEhC,QAAK,KAAK,UAAU,WAAW,GAAI;AAElC,WAAK,aAAa,IAAK,MAAM,OAAO,MAAM,KAAM;AAAA,IAEjD,OAAO;AAEN,YAAM,WAAW,KAAK,0BAA2B,KAAM;AAEvD,YAAM,IAAI,OAAQ,MAAM,QAAQ,SAAS;AACzC,YAAM,IAAI,OAAQ,MAAM,QAAQ,SAAS;AAEzC,WAAK,aAAa,IAAK,GAAG,CAAE;AAAA,IAE7B;AAAA,EAED;AAAA,EAEA,qBAAsB,OAAQ;AAE7B,QAAK,KAAK,UAAU,WAAW,GAAI;AAElC,WAAK,UAAU,IAAK,MAAM,OAAO,MAAM,KAAM;AAAA,IAE9C,OAAO;AAEN,YAAM,WAAW,KAAK,0BAA2B,KAAM;AAEvD,YAAM,IAAI,OAAQ,MAAM,QAAQ,SAAS;AACzC,YAAM,IAAI,OAAQ,MAAM,QAAQ,SAAS;AAEzC,WAAK,UAAU,IAAK,GAAG,CAAE;AAAA,IAE1B;AAAA,EAED;AAAA,EAEA,uBAAwB,OAAQ;AAE/B,UAAM,WAAW,KAAK,0BAA2B,KAAM;AAEvD,UAAM,KAAK,MAAM,QAAQ,SAAS;AAClC,UAAM,KAAK,MAAM,QAAQ,SAAS;AAElC,UAAM,WAAW,KAAK,KAAM,KAAK,KAAK,KAAK,EAAG;AAE9C,SAAK,YAAY,IAAK,GAAG,QAAS;AAAA,EAEnC;AAAA,EAEA,0BAA2B,OAAQ;AAElC,QAAK,KAAK,WAAa,MAAK,uBAAwB,KAAM;AAE1D,QAAK,KAAK,UAAY,MAAK,qBAAsB,KAAM;AAAA,EAExD;AAAA,EAEA,6BAA8B,OAAQ;AAErC,QAAK,KAAK,WAAa,MAAK,uBAAwB,KAAM;AAE1D,QAAK,KAAK,aAAe,MAAK,wBAAyB,KAAM;AAAA,EAE9D;AAAA,EAEA,uBAAwB,OAAQ;AAE/B,QAAK,KAAK,UAAU,UAAU,GAAI;AAEjC,WAAK,WAAW,IAAK,MAAM,OAAO,MAAM,KAAM;AAAA,IAE/C,OAAO;AAEN,YAAM,WAAW,KAAK,0BAA2B,KAAM;AAEvD,YAAM,IAAI,OAAQ,MAAM,QAAQ,SAAS;AACzC,YAAM,IAAI,OAAQ,MAAM,QAAQ,SAAS;AAEzC,WAAK,WAAW,IAAK,GAAG,CAAE;AAAA,IAE3B;AAEA,SAAK,aAAa,WAAY,KAAK,YAAY,KAAK,YAAa,EAAE,eAAgB,KAAK,WAAY;AAEpG,UAAM,UAAU,KAAK;AAErB,SAAK,YAAa,SAAS,KAAK,aAAa,IAAI,QAAQ,YAAa;AAEtE,SAAK,UAAW,SAAS,KAAK,aAAa,IAAI,QAAQ,YAAa;AAEpE,SAAK,aAAa,KAAM,KAAK,UAAW;AAAA,EAEzC;AAAA,EAEA,oBAAqB,OAAQ;AAE5B,QAAK,KAAK,UAAU,WAAW,GAAI;AAElC,WAAK,QAAQ,IAAK,MAAM,OAAO,MAAM,KAAM;AAAA,IAE5C,OAAO;AAEN,YAAM,WAAW,KAAK,0BAA2B,KAAM;AAEvD,YAAM,IAAI,OAAQ,MAAM,QAAQ,SAAS;AACzC,YAAM,IAAI,OAAQ,MAAM,QAAQ,SAAS;AAEzC,WAAK,QAAQ,IAAK,GAAG,CAAE;AAAA,IAExB;AAEA,SAAK,UAAU,WAAY,KAAK,SAAS,KAAK,SAAU,EAAE,eAAgB,KAAK,QAAS;AAExF,SAAK,KAAM,KAAK,UAAU,GAAG,KAAK,UAAU,CAAE;AAE9C,SAAK,UAAU,KAAM,KAAK,OAAQ;AAAA,EAEnC;AAAA,EAEA,sBAAuB,OAAQ;AAE9B,UAAM,WAAW,KAAK,0BAA2B,KAAM;AAEvD,UAAM,KAAK,MAAM,QAAQ,SAAS;AAClC,UAAM,KAAK,MAAM,QAAQ,SAAS;AAElC,UAAM,WAAW,KAAK,KAAM,KAAK,KAAK,KAAK,EAAG;AAE9C,SAAK,UAAU,IAAK,GAAG,QAAS;AAEhC,SAAK,YAAY,IAAK,GAAG,KAAK,IAAK,KAAK,UAAU,IAAI,KAAK,YAAY,GAAG,KAAK,SAAU,CAAE;AAE3F,SAAK,UAAW,KAAK,YAAY,CAAE;AAEnC,SAAK,YAAY,KAAM,KAAK,SAAU;AAEtC,UAAM,WAAY,MAAM,QAAQ,SAAS,KAAM;AAC/C,UAAM,WAAY,MAAM,QAAQ,SAAS,KAAM;AAE/C,SAAK,sBAAuB,SAAS,OAAQ;AAAA,EAE9C;AAAA,EAEA,yBAA0B,OAAQ;AAEjC,QAAK,KAAK,WAAa,MAAK,sBAAuB,KAAM;AAEzD,QAAK,KAAK,UAAY,MAAK,oBAAqB,KAAM;AAAA,EAEvD;AAAA,EAEA,4BAA6B,OAAQ;AAEpC,QAAK,KAAK,WAAa,MAAK,sBAAuB,KAAM;AAEzD,QAAK,KAAK,aAAe,MAAK,uBAAwB,KAAM;AAAA,EAE7D;AAAA;AAAA,EAIA,YAAa,OAAQ;AAEpB,SAAK,UAAU,KAAM,MAAM,SAAU;AAAA,EAEtC;AAAA,EAEA,eAAgB,OAAQ;AAEvB,WAAO,KAAK,kBAAmB,MAAM,SAAU;AAE/C,aAAU,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAO;AAElD,UAAK,KAAK,UAAW,CAAE,KAAK,MAAM,WAAY;AAE7C,aAAK,UAAU,OAAQ,GAAG,CAAE;AAC5B;AAAA,MAED;AAAA,IAED;AAAA,EAED;AAAA,EAEA,mBAAoB,OAAQ;AAE3B,aAAU,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAO;AAElD,UAAK,KAAK,UAAW,CAAE,KAAK,MAAM,UAAY,QAAO;AAAA,IAEtD;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,cAAe,OAAQ;AAEtB,QAAI,WAAW,KAAK,kBAAmB,MAAM,SAAU;AAEvD,QAAK,aAAa,QAAY;AAE7B,iBAAW,IAAI,QAAQ;AACvB,WAAK,kBAAmB,MAAM,SAAU,IAAI;AAAA,IAE7C;AAEA,aAAS,IAAK,MAAM,OAAO,MAAM,KAAM;AAAA,EAExC;AAAA,EAEA,0BAA2B,OAAQ;AAElC,UAAM,YAAc,MAAM,cAAc,KAAK,UAAW,CAAE,IAAM,KAAK,UAAW,CAAE,IAAI,KAAK,UAAW,CAAE;AAExG,WAAO,KAAK,kBAAmB,SAAU;AAAA,EAE1C;AAAA;AAAA,EAIA,kBAAmB,OAAQ;AAE1B,UAAM,OAAO,MAAM;AAGnB,UAAM,WAAW;AAAA,MAChB,SAAS,MAAM;AAAA,MACf,SAAS,MAAM;AAAA,MACf,QAAQ,MAAM;AAAA,IACf;AAEA,YAAS,MAAO;AAAA,MAEf,KAAK;AACJ,iBAAS,UAAU;AACnB;AAAA,MAED,KAAK;AACJ,iBAAS,UAAU;AACnB;AAAA,IAEF;AAGA,QAAK,MAAM,WAAW,CAAE,KAAK,gBAAiB;AAE7C,eAAS,UAAU;AAAA,IAEpB;AAEA,WAAO;AAAA,EAER;AAED;AAEA,SAAS,cAAe,OAAQ;AAE/B,MAAK,KAAK,YAAY,MAAQ;AAE9B,MAAK,KAAK,UAAU,WAAW,GAAI;AAElC,SAAK,WAAW,kBAAmB,MAAM,SAAU;AAEnD,SAAK,WAAW,iBAAkB,eAAe,KAAK,cAAe;AACrE,SAAK,WAAW,iBAAkB,aAAa,KAAK,YAAa;AAAA,EAElE;AAIA,MAAK,KAAK,mBAAoB,KAAM,EAAI;AAIxC,OAAK,YAAa,KAAM;AAExB,MAAK,MAAM,gBAAgB,SAAU;AAEpC,SAAK,cAAe,KAAM;AAAA,EAE3B,OAAO;AAEN,SAAK,aAAc,KAAM;AAAA,EAE1B;AAED;AAEA,SAAS,cAAe,OAAQ;AAE/B,MAAK,KAAK,YAAY,MAAQ;AAE9B,MAAK,MAAM,gBAAgB,SAAU;AAEpC,SAAK,aAAc,KAAM;AAAA,EAE1B,OAAO;AAEN,SAAK,aAAc,KAAM;AAAA,EAE1B;AAED;AAEA,SAAS,YAAa,OAAQ;AAE7B,OAAK,eAAgB,KAAM;AAE3B,UAAS,KAAK,UAAU,QAAS;AAAA,IAEhC,KAAK;AAEJ,WAAK,WAAW,sBAAuB,MAAM,SAAU;AAEvD,WAAK,WAAW,oBAAqB,eAAe,KAAK,cAAe;AACxE,WAAK,WAAW,oBAAqB,aAAa,KAAK,YAAa;AAEpE,WAAK,cAAe,SAAU;AAE9B,WAAK,QAAQ,OAAO;AAEpB;AAAA,IAED,KAAK;AAEJ,YAAM,YAAY,KAAK,UAAW,CAAE;AACpC,YAAM,WAAW,KAAK,kBAAmB,SAAU;AAGnD,WAAK,cAAe,EAAE,WAAsB,OAAO,SAAS,GAAG,OAAO,SAAS,EAAE,CAAE;AAEnF;AAAA,EAEF;AAED;AAEA,SAAS,YAAa,OAAQ;AAE7B,MAAI;AAEJ,UAAS,MAAM,QAAS;AAAA,IAEvB,KAAK;AAEJ,oBAAc,KAAK,aAAa;AAChC;AAAA,IAED,KAAK;AAEJ,oBAAc,KAAK,aAAa;AAChC;AAAA,IAED,KAAK;AAEJ,oBAAc,KAAK,aAAa;AAChC;AAAA,IAED;AAEC,oBAAc;AAAA,EAEhB;AAEA,UAAS,aAAc;AAAA,IAEtB,KAAK,MAAM;AAEV,UAAK,KAAK,eAAe,MAAQ;AAEjC,WAAK,sBAAuB,KAAM;AAElC,WAAK,QAAQ,OAAO;AAEpB;AAAA,IAED,KAAK,MAAM;AAEV,UAAK,MAAM,WAAW,MAAM,WAAW,MAAM,UAAW;AAEvD,YAAK,KAAK,cAAc,MAAQ;AAEhC,aAAK,oBAAqB,KAAM;AAEhC,aAAK,QAAQ,OAAO;AAAA,MAErB,OAAO;AAEN,YAAK,KAAK,iBAAiB,MAAQ;AAEnC,aAAK,uBAAwB,KAAM;AAEnC,aAAK,QAAQ,OAAO;AAAA,MAErB;AAEA;AAAA,IAED,KAAK,MAAM;AAEV,UAAK,MAAM,WAAW,MAAM,WAAW,MAAM,UAAW;AAEvD,YAAK,KAAK,iBAAiB,MAAQ;AAEnC,aAAK,uBAAwB,KAAM;AAEnC,aAAK,QAAQ,OAAO;AAAA,MAErB,OAAO;AAEN,YAAK,KAAK,cAAc,MAAQ;AAEhC,aAAK,oBAAqB,KAAM;AAEhC,aAAK,QAAQ,OAAO;AAAA,MAErB;AAEA;AAAA,IAED;AAEC,WAAK,QAAQ,OAAO;AAAA,EAEtB;AAEA,MAAK,KAAK,UAAU,OAAO,MAAO;AAEjC,SAAK,cAAe,WAAY;AAAA,EAEjC;AAED;AAEA,SAAS,YAAa,OAAQ;AAE7B,UAAS,KAAK,OAAQ;AAAA,IAErB,KAAK,OAAO;AAEX,UAAK,KAAK,iBAAiB,MAAQ;AAEnC,WAAK,uBAAwB,KAAM;AAEnC;AAAA,IAED,KAAK,OAAO;AAEX,UAAK,KAAK,eAAe,MAAQ;AAEjC,WAAK,sBAAuB,KAAM;AAElC;AAAA,IAED,KAAK,OAAO;AAEX,UAAK,KAAK,cAAc,MAAQ;AAEhC,WAAK,oBAAqB,KAAM;AAEhC;AAAA,EAEF;AAED;AAEA,SAAS,aAAc,OAAQ;AAE9B,MAAK,KAAK,YAAY,SAAS,KAAK,eAAe,SAAS,KAAK,UAAU,OAAO,KAAO;AAEzF,QAAM,eAAe;AAErB,OAAK,cAAe,WAAY;AAEhC,OAAK,kBAAmB,KAAK,kBAAmB,KAAM,CAAE;AAExD,OAAK,cAAe,SAAU;AAE/B;AAEA,SAAS,UAAW,OAAQ;AAE3B,MAAK,KAAK,YAAY,MAAQ;AAE9B,OAAK,eAAgB,KAAM;AAE5B;AAEA,SAAS,aAAc,OAAQ;AAE9B,OAAK,cAAe,KAAM;AAE1B,UAAS,KAAK,UAAU,QAAS;AAAA,IAEhC,KAAK;AAEJ,cAAS,KAAK,QAAQ,KAAM;AAAA,QAE3B,KAAK,MAAM;AAEV,cAAK,KAAK,iBAAiB,MAAQ;AAEnC,eAAK,wBAAyB,KAAM;AAEpC,eAAK,QAAQ,OAAO;AAEpB;AAAA,QAED,KAAK,MAAM;AAEV,cAAK,KAAK,cAAc,MAAQ;AAEhC,eAAK,qBAAsB,KAAM;AAEjC,eAAK,QAAQ,OAAO;AAEpB;AAAA,QAED;AAEC,eAAK,QAAQ,OAAO;AAAA,MAEtB;AAEA;AAAA,IAED,KAAK;AAEJ,cAAS,KAAK,QAAQ,KAAM;AAAA,QAE3B,KAAK,MAAM;AAEV,cAAK,KAAK,eAAe,SAAS,KAAK,cAAc,MAAQ;AAE7D,eAAK,0BAA2B,KAAM;AAEtC,eAAK,QAAQ,OAAO;AAEpB;AAAA,QAED,KAAK,MAAM;AAEV,cAAK,KAAK,eAAe,SAAS,KAAK,iBAAiB,MAAQ;AAEhE,eAAK,6BAA8B,KAAM;AAEzC,eAAK,QAAQ,OAAO;AAEpB;AAAA,QAED;AAEC,eAAK,QAAQ,OAAO;AAAA,MAEtB;AAEA;AAAA,IAED;AAEC,WAAK,QAAQ,OAAO;AAAA,EAEtB;AAEA,MAAK,KAAK,UAAU,OAAO,MAAO;AAEjC,SAAK,cAAe,WAAY;AAAA,EAEjC;AAED;AAEA,SAAS,YAAa,OAAQ;AAE7B,OAAK,cAAe,KAAM;AAE1B,UAAS,KAAK,OAAQ;AAAA,IAErB,KAAK,OAAO;AAEX,UAAK,KAAK,iBAAiB,MAAQ;AAEnC,WAAK,uBAAwB,KAAM;AAEnC,WAAK,OAAO;AAEZ;AAAA,IAED,KAAK,OAAO;AAEX,UAAK,KAAK,cAAc,MAAQ;AAEhC,WAAK,oBAAqB,KAAM;AAEhC,WAAK,OAAO;AAEZ;AAAA,IAED,KAAK,OAAO;AAEX,UAAK,KAAK,eAAe,SAAS,KAAK,cAAc,MAAQ;AAE7D,WAAK,yBAA0B,KAAM;AAErC,WAAK,OAAO;AAEZ;AAAA,IAED,KAAK,OAAO;AAEX,UAAK,KAAK,eAAe,SAAS,KAAK,iBAAiB,MAAQ;AAEhE,WAAK,4BAA6B,KAAM;AAExC,WAAK,OAAO;AAEZ;AAAA,IAED;AAEC,WAAK,QAAQ,OAAO;AAAA,EAEtB;AAED;AAEA,SAAS,cAAe,OAAQ;AAE/B,MAAK,KAAK,YAAY,MAAQ;AAE9B,QAAM,eAAe;AAEtB;AAEA,SAAS,qBAAsB,OAAQ;AAEtC,MAAK,MAAM,QAAQ,WAAY;AAE9B,SAAK,iBAAiB;AAEtB,UAAM,WAAW,KAAK,WAAW,YAAY;AAE7C,aAAS,iBAAkB,SAAS,KAAK,qBAAqB,EAAE,SAAS,MAAM,SAAS,KAAK,CAAE;AAAA,EAEhG;AAED;AAEA,SAAS,mBAAoB,OAAQ;AAEpC,MAAK,MAAM,QAAQ,WAAY;AAE9B,SAAK,iBAAiB;AAEtB,UAAM,WAAW,KAAK,WAAW,YAAY;AAE7C,aAAS,oBAAqB,SAAS,KAAK,qBAAqB,EAAE,SAAS,MAAM,SAAS,KAAK,CAAE;AAAA,EAEnG;AAED;", "names": []}