<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>哆啦A梦 - Three.js 3D模型</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
            font-family: 'Arial', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        
        h1 {
            color: #0066CC;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            font-size: 2.5em;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border: 3px solid #0066CC;
        }
        
        #doraemon-demo {
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .info {
            margin-top: 20px;
            text-align: center;
            color: #333;
            font-size: 16px;
        }
        
        .controls {
            margin-top: 15px;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>🤖 哆啦A梦 - Three.js 3D模型</h1>
    
    <div class="container">
        <canvas id="doraemon-demo"></canvas>
        
        <div class="info">
            <p>🎯 <strong>经典哆啦A梦造型</strong></p>
            <p>圆滚滚的蓝色身体 • 大大的眼睛 • 红色圆鼻头 • 经典铃铛</p>
        </div>
        
        <div class="controls">
            <p>🖱️ 鼠标拖拽旋转 • 滚轮缩放 • 右键平移</p>
        </div>
    </div>

    <script type="module" src="./doraemon-simple.ts"></script>
</body>
</html>
