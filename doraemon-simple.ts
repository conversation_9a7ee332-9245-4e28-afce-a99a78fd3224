import * as THREE from 'three'
import { OrbitControls } from 'three/addons/controls/OrbitControls.js'
//stats 性能监控
import Stats from 'three/addons/libs/stats.module.js';

// 获取canvas元素
const canvas = document.querySelector('#doraemon-demo') as HTMLCanvasElement
if (!canvas) {
    console.error('Canvas element not found!')
} else {
    initDoraemonDemo()
}

function initDoraemonDemo() {
    //创建一个场景
    const scene = new THREE.Scene()
    scene.background = new THREE.Color(0x87CEEB) // 天蓝色背景
    const width = 800
    const height = 600
    
    //光源设置
    const light = new THREE.DirectionalLight(0xffffff, 1)
    light.position.set(100, 100, 100)
    light.castShadow = true
    scene.add(light)
    
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4)
    scene.add(ambientLight)
    
    //创建哆啦A梦材质
    const blueMaterial = new THREE.MeshLambertMaterial({ color: 0x0066CC })
    const whiteMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFFF })
    const redMaterial = new THREE.MeshLambertMaterial({ color: 0xFF0000 })
    const blackMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 })
    const yellowMaterial = new THREE.MeshLambertMaterial({ color: 0xFFD700 })
    
    //创建哆啦A梦组
    const doraemon = new THREE.Group()
    
    //头部 - 大圆球
    const headGeometry = new THREE.SphereGeometry(40, 32, 32)
    const head = new THREE.Mesh(headGeometry, blueMaterial)
    head.position.set(0, 20, 0)
    doraemon.add(head)
    
    //脸部白色区域
    const faceGeometry = new THREE.SphereGeometry(38, 32, 32)
    const face = new THREE.Mesh(faceGeometry, whiteMaterial)
    face.position.set(0, 20, 15)
    face.scale.set(0.9, 1, 0.6)
    doraemon.add(face)
    
    //眼睛
    const eyeGeometry = new THREE.SphereGeometry(12, 16, 16)
    const leftEye = new THREE.Mesh(eyeGeometry, whiteMaterial)
    leftEye.position.set(-12, 30, 30)
    leftEye.scale.set(1, 1.2, 0.8)
    doraemon.add(leftEye)
    
    const rightEye = new THREE.Mesh(eyeGeometry, whiteMaterial)
    rightEye.position.set(12, 30, 30)
    rightEye.scale.set(1, 1.2, 0.8)
    doraemon.add(rightEye)
    
    //眼珠
    const eyeBallGeometry = new THREE.SphereGeometry(3, 16, 16)
    const leftEyeBall = new THREE.Mesh(eyeBallGeometry, blackMaterial)
    leftEyeBall.position.set(-12, 30, 35)
    doraemon.add(leftEyeBall)
    
    const rightEyeBall = new THREE.Mesh(eyeBallGeometry, blackMaterial)
    rightEyeBall.position.set(12, 30, 35)
    doraemon.add(rightEyeBall)
    
    //鼻子
    const noseGeometry = new THREE.SphereGeometry(4, 16, 16)
    const nose = new THREE.Mesh(noseGeometry, redMaterial)
    nose.position.set(0, 20, 38)
    doraemon.add(nose)
    
    //嘴巴
    const mouthGeometry = new THREE.SphereGeometry(8, 16, 16, 0, Math.PI * 2, 0, Math.PI * 0.5)
    const mouth = new THREE.Mesh(mouthGeometry, redMaterial)
    mouth.position.set(0, 10, 35)
    mouth.rotation.x = Math.PI * 0.1
    doraemon.add(mouth)
    
    //胡须
    const whiskerGeometry = new THREE.CylinderGeometry(0.5, 0.5, 20, 8)
    for (let i = 0; i < 3; i++) {
        //左胡须
        const leftWhisker = new THREE.Mesh(whiskerGeometry, blackMaterial)
        leftWhisker.position.set(-25, 25 - i * 5, 25)
        leftWhisker.rotation.z = Math.PI / 2
        doraemon.add(leftWhisker)
        
        //右胡须
        const rightWhisker = new THREE.Mesh(whiskerGeometry, blackMaterial)
        rightWhisker.position.set(25, 25 - i * 5, 25)
        rightWhisker.rotation.z = Math.PI / 2
        doraemon.add(rightWhisker)
    }
    
    //身体
    const bodyGeometry = new THREE.SphereGeometry(35, 32, 32)
    const body = new THREE.Mesh(bodyGeometry, blueMaterial)
    body.position.set(0, -25, 0)
    body.scale.set(1, 1.2, 1)
    doraemon.add(body)
    
    //肚子
    const bellyGeometry = new THREE.SphereGeometry(30, 32, 32)
    const belly = new THREE.Mesh(bellyGeometry, whiteMaterial)
    belly.position.set(0, -25, 18)
    belly.scale.set(1, 1.2, 0.6)
    doraemon.add(belly)
    
    //项圈
    const collarGeometry = new THREE.TorusGeometry(42, 3, 8, 32)
    const collar = new THREE.Mesh(collarGeometry, redMaterial)
    collar.position.set(0, -5, 0)
    doraemon.add(collar)
    
    //铃铛
    const bellGeometry = new THREE.SphereGeometry(5, 16, 16)
    const bell = new THREE.Mesh(bellGeometry, yellowMaterial)
    bell.position.set(0, -5, 25)
    doraemon.add(bell)
    
    //手臂
    const armGeometry = new THREE.CapsuleGeometry(5, 15, 8, 16)
    const leftArm = new THREE.Mesh(armGeometry, blueMaterial)
    leftArm.position.set(-30, -15, 0)
    leftArm.rotation.z = Math.PI / 6
    doraemon.add(leftArm)
    
    const rightArm = new THREE.Mesh(armGeometry, blueMaterial)
    rightArm.position.set(30, -15, 0)
    rightArm.rotation.z = -Math.PI / 6
    doraemon.add(rightArm)
    
    //手
    const handGeometry = new THREE.SphereGeometry(8, 16, 16)
    const leftHand = new THREE.Mesh(handGeometry, whiteMaterial)
    leftHand.position.set(-35, -25, 0)
    doraemon.add(leftHand)
    
    const rightHand = new THREE.Mesh(handGeometry, whiteMaterial)
    rightHand.position.set(35, -25, 0)
    doraemon.add(rightHand)
    
    //腿
    const legGeometry = new THREE.CylinderGeometry(6, 6, 12, 16)
    const leftLeg = new THREE.Mesh(legGeometry, blueMaterial)
    leftLeg.position.set(-12, -50, 0)
    doraemon.add(leftLeg)
    
    const rightLeg = new THREE.Mesh(legGeometry, blueMaterial)
    rightLeg.position.set(12, -50, 0)
    doraemon.add(rightLeg)
    
    //脚
    const footGeometry = new THREE.SphereGeometry(10, 16, 16)
    const leftFoot = new THREE.Mesh(footGeometry, whiteMaterial)
    leftFoot.position.set(-12, -60, 8)
    leftFoot.scale.set(1.2, 0.6, 1.5)
    doraemon.add(leftFoot)
    
    const rightFoot = new THREE.Mesh(footGeometry, whiteMaterial)
    rightFoot.position.set(12, -60, 8)
    rightFoot.scale.set(1.2, 0.6, 1.5)
    doraemon.add(rightFoot)
    
    //将哆啦A梦添加到场景中
    scene.add(doraemon)
    
    //辅助坐标轴
    const axesHelper = new THREE.AxesHelper(80);
    scene.add(axesHelper)
    
    //创建一个相机
    const camera = new THREE.PerspectiveCamera(45, width / height, 1, 1000)
    camera.position.set(150, 50, 150)
    camera.lookAt(doraemon.position)
    scene.add(camera)
    
    //创建渲染器
    const renderer = new THREE.WebGLRenderer({
        canvas: canvas,
        antialias: true,
    })
    renderer.setSize(width, height)
    renderer.shadowMap.enabled = true
    
    const stats = new Stats()
    stats.dom.style.position = 'absolute'
    stats.dom.style.top = '10px'
    stats.dom.style.left = '10px'
    stats.dom.style.zIndex = '100'
    let statsContainer: HTMLElement | null = null
    
    //创建控制器
    const controls = new OrbitControls(camera, renderer.domElement)
    controls.enableDamping = true
    controls.dampingFactor = 0.05
    
    const animate = () => {
        requestAnimationFrame(animate)
        renderer.render(scene, camera)
        
        // 简单的动画效果
        const time = Date.now() * 0.001
        
        // 眼珠跟随时间移动
        leftEyeBall.position.x = -12 + Math.sin(time * 0.5) * 2
        rightEyeBall.position.x = 12 + Math.sin(time * 0.5) * 2
        
        // 铃铛摆动
        bell.rotation.z = Math.sin(time * 2) * 0.1
        
        // 整体轻微摆动
        doraemon.rotation.y = Math.sin(time * 0.5) * 0.05
        
        controls.update()
        
        // stats更新
        if (statsContainer && document.body.contains(statsContainer)) {
            stats.update()
        }
    }
    animate()
    
    // 监听canvas的显示状态
    const observer = new MutationObserver(() => {
        const isVisible = canvas.style.zIndex === '1000'
        if (isVisible && !statsContainer) {
            statsContainer = document.createElement('div')
            statsContainer.style.position = 'fixed'
            statsContainer.style.top = 'calc(50% - 140px)'
            statsContainer.style.left = 'calc(50% - 190px)'
            statsContainer.style.zIndex = '1001'
            statsContainer.appendChild(stats.dom)
            document.body.appendChild(statsContainer)
        } else if (!isVisible && statsContainer) {
            if (document.body.contains(statsContainer)) {
                document.body.removeChild(statsContainer)
            }
            statsContainer = null
        }
    })
    
    observer.observe(canvas, {
        attributes: true,
        attributeFilter: ['style']
    })
}
