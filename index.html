<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js 学习项目</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            font-size: 3em;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .project-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        
        .project-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        
        .project-card:hover {
            transform: translateY(-5px);
        }
        
        .project-card h2 {
            font-size: 2em;
            margin-bottom: 15px;
            color: #FFD700;
        }
        
        .project-card p {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        #basic-demo {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: -1;
            opacity: 0.1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 Three.js 学习项目</h1>
        
        <div class="project-grid">
            <div class="project-card">
                <h2>🤖 哆啦A梦</h2>
                <p>
                    使用Three.js创建的完整3D哆啦A梦模型！<br>
                    包含所有经典特征，支持动画和交互控制。<br>
                    眼珠会动，铃铛会摆，尾巴会摇！
                </p>
                <a href="./doraemon.html" class="btn">查看哆啦A梦 🚀</a>
            </div>

            <div class="project-card">
                <h2>🤖 哆啦A梦2</h2>
                <p>
                    使用Three.js创建的完整3D哆啦A梦模型！<br>
                    包含所有经典特征，支持动画和交互控制。<br>
                    眼珠会动，铃铛会摆，尾巴会摇！
                </p>
                <a href="./doraemon-simple.html" class="btn">查看哆啦A梦 🚀</a>
            </div>
            
            <div class="project-card">
                <h2>⚡ 基础演示</h2>
                <p>
                    Three.js基础学习代码，包含：<br>
                    球体几何、材质、光照、控制器等<br>
                    适合初学者了解Three.js基础概念
                </p>
                <a href="#" onclick="showBasicDemo()" class="btn">查看基础演示 📚</a>
            </div>
        </div>
        
        <div style="margin-top: 50px; font-size: 1.2em; opacity: 0.8;">
            <p>🎯 学习目标：掌握Three.js的基础概念和3D建模技巧</p>
            <p>🛠️ 技术栈：Three.js + TypeScript + Vite</p>
        </div>
    </div>
    
    <canvas id="basic-demo" width="400" height="300"></canvas>
    
    <script>
        function showBasicDemo() {
            const canvas = document.getElementById('basic-demo');
            canvas.style.position = 'fixed';
            canvas.style.top = '50%';
            canvas.style.left = '50%';
            canvas.style.transform = 'translate(-50%, -50%)';
            canvas.style.zIndex = '1000';
            canvas.style.opacity = '1';
            canvas.style.border = '2px solid #FFD700';
            canvas.style.borderRadius = '10px';
            canvas.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.5)';
            
            // 添加关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '✕';
            closeBtn.style.position = 'fixed';
            closeBtn.style.top = 'calc(50% - 170px)';
            closeBtn.style.right = 'calc(50% - 220px)';
            closeBtn.style.zIndex = '1001';
            closeBtn.style.background = '#FF6B6B';
            closeBtn.style.color = 'white';
            closeBtn.style.border = 'none';
            closeBtn.style.borderRadius = '50%';
            closeBtn.style.width = '30px';
            closeBtn.style.height = '30px';
            closeBtn.style.cursor = 'pointer';
            closeBtn.style.fontSize = '16px';
            closeBtn.onclick = () => {
                canvas.style.zIndex = '-1';
                canvas.style.opacity = '0.1';
                document.body.removeChild(closeBtn);
            };
            document.body.appendChild(closeBtn);
        }
    </script>
    
    <script type="module" src="./index.ts"></script>
</body>
</html>