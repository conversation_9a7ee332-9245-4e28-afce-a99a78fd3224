<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>哆啦A梦 - Three.js</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Comic+Neue:wght@400;700&display=swap');

        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #87CEEB 0%, #FFE4E1 50%, #98FB98 100%);
            font-family: 'Comic Neue', cursive;
            overflow: hidden;
            animation: backgroundShift 10s ease-in-out infinite alternate;
        }

        @keyframes backgroundShift {
            0% { background: linear-gradient(135deg, #87CEEB 0%, #FFE4E1 50%, #98FB98 100%); }
            100% { background: linear-gradient(135deg, #98FB98 0%, #FFE4E1 50%, #87CEEB 100%); }
        }

        #canvas {
            display: block;
            margin: 0 auto;
            border: 3px solid #4169E1;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(65, 105, 225, 0.3);
            animation: canvasGlow 3s ease-in-out infinite alternate;
        }

        @keyframes canvasGlow {
            0% { box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(65, 105, 225, 0.3); }
            100% { box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 30px rgba(65, 105, 225, 0.6); }
        }

        .title {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: #4169E1;
            font-size: 32px;
            font-weight: 700;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
            z-index: 100;
            animation: titleBounce 2s ease-in-out infinite;
        }

        @keyframes titleBounce {
            0%, 100% { transform: translateX(-50%) translateY(0px); }
            50% { transform: translateX(-50%) translateY(-5px); }
        }

        .controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: #4169E1;
            font-size: 16px;
            text-align: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px 25px;
            border-radius: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            z-index: 100;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(65, 105, 225, 0.3);
        }

        .controls div {
            margin: 5px 0;
        }

        .emoji {
            font-size: 20px;
            animation: emojiSpin 3s linear infinite;
        }

        @keyframes emojiSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        #stats {
            position: absolute !important;
            top: 80px;
            left: 10px;
            z-index: 100;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
        }

        .decoration {
            position: absolute;
            font-size: 30px;
            animation: float 4s ease-in-out infinite;
            z-index: 50;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .decoration:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .decoration:nth-child(2) { top: 20%; right: 10%; animation-delay: 1s; }
        .decoration:nth-child(3) { bottom: 30%; left: 15%; animation-delay: 2s; }
        .decoration:nth-child(4) { bottom: 20%; right: 15%; animation-delay: 3s; }
    </style>
</head>
<body>
    <!-- 装饰元素 -->
    <div class="decoration">🌟</div>
    <div class="decoration">⭐</div>
    <div class="decoration">✨</div>
    <div class="decoration">💫</div>

    <div class="title">
        <span class="emoji">🤖</span>
        超可爱哆啦A梦 - Three.js 3D模型
        <span class="emoji">💙</span>
    </div>

    <canvas id="canvas"></canvas>

    <div class="controls">
        <div><strong>🖱️ 操作指南</strong></div>
        <div>🔄 鼠标拖拽旋转视角 | 🔍 滚轮缩放 | 📱 右键拖拽平移</div>
        <div>✨ 哆啦A梦会自动旋转，还会眨眼睛和摆尾巴哦！</div>
        <div>💖 看他可爱的小动作，是不是很萌呢？</div>
    </div>

    <script type="module" src="./doraemon.ts"></script>
</body>
</html> 