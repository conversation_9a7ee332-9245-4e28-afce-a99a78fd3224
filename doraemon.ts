import * as THREE from 'three'
import { OrbitControls } from 'three/addons/controls/OrbitControls.js'
import Stats from 'three/addons/libs/stats.module.js'

// 创建场景
const scene = new THREE.Scene()
scene.background = new THREE.Color(0xFFD700) // 金黄色背景，更符合哆啦A梦的温馨感

const width = 800
const height = 600

// 创建相机
const camera = new THREE.PerspectiveCamera(45, width / height, 1, 1000)
camera.position.set(0, 0, 150)
camera.lookAt(0, 0, 0)

// 创建渲染器
const renderer = new THREE.WebGLRenderer({
    canvas: document.querySelector('#canvas') as HTMLCanvasElement,
    antialias: true,
})
renderer.setSize(width, height)
renderer.shadowMap.enabled = true
renderer.shadowMap.type = THREE.PCFSoftShadowMap

// 添加光源
const ambientLight = new THREE.AmbientLight(0xffffff, 0.7)
scene.add(ambientLight)

const directionalLight = new THREE.DirectionalLight(0xffffff, 0.9)
directionalLight.position.set(30, 50, 30)
directionalLight.castShadow = true
directionalLight.shadow.mapSize.width = 2048
directionalLight.shadow.mapSize.height = 2048
scene.add(directionalLight)

// 添加补光
const fillLight = new THREE.DirectionalLight(0xffffff, 0.4)
fillLight.position.set(-30, 30, -30)
scene.add(fillLight)

// 创建哆啦A梦群组
const doraemon = new THREE.Group()

// 改进的材质定义
const blueMaterial = new THREE.MeshLambertMaterial({ 
    color: 0x0080FF,  // 更鲜艳的蓝色
})
const whiteMaterial = new THREE.MeshLambertMaterial({ 
    color: 0xFFFFFF,  // 纯白色
})
const redMaterial = new THREE.MeshLambertMaterial({ 
    color: 0xFF0000,  // 鲜红色
})
const blackMaterial = new THREE.MeshLambertMaterial({ 
    color: 0x000000,  // 纯黑色
})
const yellowMaterial = new THREE.MeshLambertMaterial({ 
    color: 0xFFD700,  // 金黄色
})

// 🔵 创建圆形头部
const headGeometry = new THREE.SphereGeometry(30, 32, 32)
const head = new THREE.Mesh(headGeometry, blueMaterial)
head.position.set(0, 15, 0)
head.castShadow = true
doraemon.add(head)

// 🤍 创建白色脸部区域
const faceGeometry = new THREE.SphereGeometry(28, 32, 32)
const face = new THREE.Mesh(faceGeometry, whiteMaterial)
face.position.set(0, 12, 15)
face.scale.set(1, 1.1, 0.4) // 脸部区域
face.castShadow = true
doraemon.add(face)

// 👀 创建巨大的椭圆形眼睛（几乎贴在一起）
const eyeGeometry = new THREE.SphereGeometry(16, 32, 32)
const leftEye = new THREE.Mesh(eyeGeometry, whiteMaterial)
leftEye.position.set(-5, 20, 25) // 几乎贴在一起
leftEye.scale.set(1, 1.5, 0.3) // 巨大的椭圆形
leftEye.castShadow = true
doraemon.add(leftEye)

const rightEye = new THREE.Mesh(eyeGeometry, whiteMaterial)
rightEye.position.set(5, 20, 25) // 几乎贴在一起
rightEye.scale.set(1, 1.5, 0.3) // 巨大的椭圆形
rightEye.castShadow = true
doraemon.add(rightEye)

// ⚫ 创建小黑眼珠（位于眼睛中央偏下）
const eyeBallGeometry = new THREE.SphereGeometry(2, 16, 16)
const leftEyeBall = new THREE.Mesh(eyeBallGeometry, blackMaterial)
leftEyeBall.position.set(-5, 18, 28) // 中央偏下
doraemon.add(leftEyeBall)

const rightEyeBall = new THREE.Mesh(eyeBallGeometry, blackMaterial)
rightEyeBall.position.set(5, 18, 28) // 中央偏下
doraemon.add(rightEyeBall)

// 🔴 创建红色圆鼻子
const noseGeometry = new THREE.SphereGeometry(2.5, 16, 16)
const nose = new THREE.Mesh(noseGeometry, redMaterial)
nose.position.set(0, 8, 28)
nose.castShadow = true
doraemon.add(nose)

// 😊 创建简单的弯曲嘴巴（黑色弧线）
const mouthGeometry = new THREE.TorusGeometry(8, 0.5, 8, 16, Math.PI)
const mouth = new THREE.Mesh(mouthGeometry, blackMaterial)
mouth.position.set(0, 2, 26)
mouth.rotation.z = Math.PI // 翻转成微笑
doraemon.add(mouth)

// 📏 创建6根直线胡须
const whiskerGeometry = new THREE.CylinderGeometry(0.3, 0.3, 15, 8)

// 左边胡须
const leftWhisker1 = new THREE.Mesh(whiskerGeometry, blackMaterial)
leftWhisker1.position.set(-22, 15, 22)
leftWhisker1.rotation.z = Math.PI / 2
leftWhisker1.rotation.y = Math.PI / 10
doraemon.add(leftWhisker1)

const leftWhisker2 = new THREE.Mesh(whiskerGeometry, blackMaterial)
leftWhisker2.position.set(-22, 10, 22)
leftWhisker2.rotation.z = Math.PI / 2
doraemon.add(leftWhisker2)

const leftWhisker3 = new THREE.Mesh(whiskerGeometry, blackMaterial)
leftWhisker3.position.set(-22, 5, 22)
leftWhisker3.rotation.z = Math.PI / 2
leftWhisker3.rotation.y = -Math.PI / 10
doraemon.add(leftWhisker3)

// 右边胡须
const rightWhisker1 = new THREE.Mesh(whiskerGeometry, blackMaterial)
rightWhisker1.position.set(22, 15, 22)
rightWhisker1.rotation.z = Math.PI / 2
rightWhisker1.rotation.y = -Math.PI / 10
doraemon.add(rightWhisker1)

const rightWhisker2 = new THREE.Mesh(whiskerGeometry, blackMaterial)
rightWhisker2.position.set(22, 10, 22)
rightWhisker2.rotation.z = Math.PI / 2
doraemon.add(rightWhisker2)

const rightWhisker3 = new THREE.Mesh(whiskerGeometry, blackMaterial)
rightWhisker3.position.set(22, 5, 22)
rightWhisker3.rotation.z = Math.PI / 2
rightWhisker3.rotation.y = Math.PI / 10
doraemon.add(rightWhisker3)

// 🔵 创建圆滚滚的身体
const bodyGeometry = new THREE.SphereGeometry(25, 32, 32)
const body = new THREE.Mesh(bodyGeometry, blueMaterial)
body.position.set(0, -25, 0)
body.scale.set(1, 1.4, 1) // 稍微拉长的椭圆形身体
body.castShadow = true
doraemon.add(body)

// 🤍 创建大面积白色肚子
const bellyGeometry = new THREE.SphereGeometry(23, 32, 32)
const belly = new THREE.Mesh(bellyGeometry, whiteMaterial)
belly.position.set(0, -25, 15)
belly.scale.set(0.95, 1.3, 0.4) // 更大更突出的肚子
belly.castShadow = true
doraemon.add(belly)

// 创建简单的平面口袋
const pocketGeometry = new THREE.CircleGeometry(10, 32)
const pocket = new THREE.Mesh(pocketGeometry, blackMaterial)
pocket.position.set(0, -32, 19)
doraemon.add(pocket)

// 🔔 创建红色项圈
const collarGeometry = new THREE.TorusGeometry(15, 2, 8, 32)
const collar = new THREE.Mesh(collarGeometry, redMaterial)
collar.position.set(0, -5, 0)
collar.rotation.x = Math.PI / 2
collar.castShadow = true
doraemon.add(collar)

// 创建金色铃铛
const bellGeometry = new THREE.SphereGeometry(4, 16, 16)
const bell = new THREE.Mesh(bellGeometry, yellowMaterial)
bell.position.set(0, -5, 20)
bell.castShadow = true
doraemon.add(bell)

// 铃铛中心装饰
const bellCenterGeometry = new THREE.SphereGeometry(1, 8, 8)
const bellCenter = new THREE.Mesh(bellCenterGeometry, blackMaterial)
bellCenter.position.set(0, -5, 23)
doraemon.add(bellCenter)

// 🤲 创建简单的圆柱形手臂
const armGeometry = new THREE.CylinderGeometry(4, 4, 15, 16)
const leftArm = new THREE.Mesh(armGeometry, blueMaterial)
leftArm.position.set(-20, -20, 0)
leftArm.rotation.z = Math.PI / 2
leftArm.castShadow = true
doraemon.add(leftArm)

const rightArm = new THREE.Mesh(armGeometry, blueMaterial)
rightArm.position.set(20, -20, 0)
rightArm.rotation.z = Math.PI / 2
rightArm.castShadow = true
doraemon.add(rightArm)

// 创建简单的白色圆手
const handGeometry = new THREE.SphereGeometry(5, 16, 16)
const leftHand = new THREE.Mesh(handGeometry, whiteMaterial)
leftHand.position.set(-28, -20, 0)
leftHand.castShadow = true
doraemon.add(leftHand)

const rightHand = new THREE.Mesh(handGeometry, whiteMaterial)
rightHand.position.set(28, -20, 0)
rightHand.castShadow = true
doraemon.add(rightHand)

// 🦵 创建腿
const legGeometry = new THREE.CylinderGeometry(5, 5, 12, 16)
const leftLeg = new THREE.Mesh(legGeometry, blueMaterial)
leftLeg.position.set(-10, -50, 0)
leftLeg.castShadow = true
doraemon.add(leftLeg)

const rightLeg = new THREE.Mesh(legGeometry, blueMaterial)
rightLeg.position.set(10, -50, 0)
rightLeg.castShadow = true
doraemon.add(rightLeg)

// 创建白色脚
const footGeometry = new THREE.SphereGeometry(6, 16, 16)
const leftFoot = new THREE.Mesh(footGeometry, whiteMaterial)
leftFoot.position.set(-10, -58, 4)
leftFoot.scale.set(1.2, 0.6, 1.6)
leftFoot.castShadow = true
doraemon.add(leftFoot)

const rightFoot = new THREE.Mesh(footGeometry, whiteMaterial)
rightFoot.position.set(10, -58, 4)
rightFoot.scale.set(1.2, 0.6, 1.6)
rightFoot.castShadow = true
doraemon.add(rightFoot)

// 创建小尾巴
const tailGeometry = new THREE.SphereGeometry(2, 16, 16)
const tail = new THREE.Mesh(tailGeometry, redMaterial)
tail.position.set(0, -20, -25)
tail.castShadow = true
doraemon.add(tail)

// 将哆啦A梦添加到场景
scene.add(doraemon)

// 创建地面
const groundGeometry = new THREE.PlaneGeometry(200, 200)
const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 })
const ground = new THREE.Mesh(groundGeometry, groundMaterial)
ground.rotation.x = -Math.PI / 2
ground.position.y = -70
ground.receiveShadow = true
scene.add(ground)

// 创建控制器
const controls = new OrbitControls(camera, renderer.domElement)
controls.enableDamping = true
controls.dampingFactor = 0.05
controls.minDistance = 60
controls.maxDistance = 300
controls.autoRotate = true
controls.autoRotateSpeed = 0.8

// 创建性能监控
const stats = new Stats()
document.body.appendChild(stats.dom)

// 简化的动画效果
function animate() {
    requestAnimationFrame(animate)
    
    const time = Date.now() * 0.001
    
    // 眼珠轻微移动
    leftEyeBall.position.x = -5 + Math.sin(time * 0.5) * 0.8
    rightEyeBall.position.x = 5 + Math.sin(time * 0.5) * 0.8
    
    // 铃铛摆动
    bell.rotation.z = Math.sin(time * 2) * 0.15
    bellCenter.rotation.z = Math.sin(time * 2) * 0.15
    
    // 尾巴摆动
    tail.rotation.y = Math.sin(time * 2.5) * 0.4
    tail.rotation.x = Math.sin(time * 2) * 0.2
    
    // 轻微呼吸
    doraemon.scale.y = 1 + Math.sin(time * 1.2) * 0.02
    
    // 眨眼动画
    if (Math.sin(time * 0.5) > 0.95) {
        leftEye.scale.y = 0.1
        rightEye.scale.y = 0.1
    } else {
        leftEye.scale.y = 1.5
        rightEye.scale.y = 1.5
    }
    
    controls.update()
    renderer.render(scene, camera)
    stats.update()
}

// 窗口大小调整
window.addEventListener('resize', () => {
    camera.aspect = window.innerWidth / window.innerHeight
    camera.updateProjectionMatrix()
    renderer.setSize(window.innerWidth, window.innerHeight)
})

// 启动动画
animate()

console.log('🤖 哆啦A梦经典造型修正完成！简洁可爱！✨') 