import * as THREE from 'three'
import { OrbitControls } from 'three/addons/controls/OrbitControls.js'
import Stats from 'three/addons/libs/stats.module.js'

// 创建场景
const scene = new THREE.Scene()
scene.background = new THREE.Color(0x87CEEB) // 天蓝色背景

const width = 800
const height = 600

// 创建相机
const camera = new THREE.PerspectiveCamera(45, width / height, 1, 1000)
camera.position.set(0, 30, 200)
camera.lookAt(0, 0, 0)

// 创建渲染器
const renderer = new THREE.WebGLRenderer({
    canvas: document.querySelector('#canvas') as HTMLCanvasElement,
    antialias: true,
})
renderer.setSize(width, height)
renderer.shadowMap.enabled = true
renderer.shadowMap.type = THREE.PCFSoftShadowMap
renderer.toneMapping = THREE.ACESFilmicToneMapping
renderer.toneMappingExposure = 1.0

// 添加光源
const ambientLight = new THREE.AmbientLight(0xffffff, 0.4)
scene.add(ambientLight)

const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
directionalLight.position.set(50, 100, 50)
directionalLight.castShadow = true
directionalLight.shadow.mapSize.width = 4096
directionalLight.shadow.mapSize.height = 4096
directionalLight.shadow.camera.near = 0.5
directionalLight.shadow.camera.far = 500
scene.add(directionalLight)

// 添加补光
const fillLight = new THREE.DirectionalLight(0xffffff, 0.3)
fillLight.position.set(-50, 50, -50)
scene.add(fillLight)

// 创建哆啦A梦群组
const doraemon = new THREE.Group()

// 高级材质定义 - 使用PBR材质
const blueMaterial = new THREE.MeshStandardMaterial({ 
    color: 0x0080FF,
    roughness: 0.3,
    metalness: 0.1,
    envMapIntensity: 0.5
})

const whiteMaterial = new THREE.MeshStandardMaterial({ 
    color: 0xFFFFFF,
    roughness: 0.2,
    metalness: 0.0,
    envMapIntensity: 0.3
})

const redMaterial = new THREE.MeshStandardMaterial({ 
    color: 0xFF0000,
    roughness: 0.4,
    metalness: 0.0,
    envMapIntensity: 0.2
})

const blackMaterial = new THREE.MeshStandardMaterial({ 
    color: 0x000000,
    roughness: 0.8,
    metalness: 0.0
})

const yellowMaterial = new THREE.MeshPhysicalMaterial({ 
    color: 0xFFD700,
    roughness: 0.1,
    metalness: 0.8,
    clearcoat: 0.5,
    clearcoatRoughness: 0.1
})

const darkRedMaterial = new THREE.MeshStandardMaterial({ 
    color: 0x8B0000,
    roughness: 0.6,
    metalness: 0.0
})

// 创建眼睛高光材质
const eyeHighlightMaterial = new THREE.MeshBasicMaterial({ 
    color: 0xFFFFFF,
    transparent: true,
    opacity: 0.9
})

// 创建头部（蓝色部分）- 更高分辨率
const headGeometry = new THREE.SphereGeometry(35, 64, 64)
const head = new THREE.Mesh(headGeometry, blueMaterial)
head.position.set(0, 15, 0)
head.castShadow = true
head.receiveShadow = true
doraemon.add(head)

// 创建脸部（白色部分 - 更大更突出）
const faceGeometry = new THREE.SphereGeometry(32, 64, 64)
const face = new THREE.Mesh(faceGeometry, whiteMaterial)
face.position.set(0, 8, 18)
face.scale.set(1, 1.1, 0.8)
face.castShadow = true
face.receiveShadow = true
doraemon.add(face)

// 创建眼睛（更大更圆，椭圆形状更可爱）
const eyeGeometry = new THREE.SphereGeometry(14, 32, 32)
const leftEye = new THREE.Mesh(eyeGeometry, whiteMaterial)
leftEye.position.set(-13, 22, 30)
leftEye.scale.set(1.1, 1.3, 0.7)
leftEye.castShadow = true
leftEye.receiveShadow = true
doraemon.add(leftEye)

const rightEye = new THREE.Mesh(eyeGeometry, whiteMaterial)
rightEye.position.set(13, 22, 30)
rightEye.scale.set(1.1, 1.3, 0.7)
rightEye.castShadow = true
rightEye.receiveShadow = true
doraemon.add(rightEye)

// 创建眼珠（更大更有神）
const eyeBallGeometry = new THREE.SphereGeometry(5, 32, 32)
const leftEyeBall = new THREE.Mesh(eyeBallGeometry, blackMaterial)
leftEyeBall.position.set(-13, 22, 38)
leftEyeBall.castShadow = true
doraemon.add(leftEyeBall)

const rightEyeBall = new THREE.Mesh(eyeBallGeometry, blackMaterial)
rightEyeBall.position.set(13, 22, 38)
rightEyeBall.castShadow = true
doraemon.add(rightEyeBall)

// 添加眼睛高光（更大更明显）
const highlightGeometry = new THREE.SphereGeometry(2, 16, 16)
const leftHighlight = new THREE.Mesh(highlightGeometry, eyeHighlightMaterial)
leftHighlight.position.set(-12, 24, 40)
doraemon.add(leftHighlight)

const rightHighlight = new THREE.Mesh(highlightGeometry, eyeHighlightMaterial)
rightHighlight.position.set(14, 24, 40)
doraemon.add(rightHighlight)

// 添加眼睛边框（黑色轮廓）
const eyeOutlineGeometry = new THREE.TorusGeometry(14, 0.8, 8, 32)
const eyeOutlineMaterial = new THREE.MeshStandardMaterial({
    color: 0x000000,
    roughness: 0.8,
    metalness: 0.0
})
const leftEyeOutline = new THREE.Mesh(eyeOutlineGeometry, eyeOutlineMaterial)
leftEyeOutline.position.set(-13, 22, 35)
leftEyeOutline.scale.set(1.1, 1.3, 1)
doraemon.add(leftEyeOutline)

const rightEyeOutline = new THREE.Mesh(eyeOutlineGeometry, eyeOutlineMaterial)
rightEyeOutline.position.set(13, 22, 35)
rightEyeOutline.scale.set(1.1, 1.3, 1)
doraemon.add(rightEyeOutline)

// 创建鼻子（更大更可爱的红色圆球）
const noseGeometry = new THREE.SphereGeometry(5, 32, 32)
const noseHighlightMaterial = new THREE.MeshPhysicalMaterial({
    color: 0xFF4444,
    roughness: 0.2,
    metalness: 0.1,
    clearcoat: 0.8,
    clearcoatRoughness: 0.1
})
const nose = new THREE.Mesh(noseGeometry, noseHighlightMaterial)
nose.position.set(0, 14, 36)
nose.castShadow = true
nose.receiveShadow = true
doraemon.add(nose)

// 添加鼻子高光
const noseHighlightGeometry = new THREE.SphereGeometry(1.5, 16, 16)
const noseHighlight = new THREE.Mesh(noseHighlightGeometry, eyeHighlightMaterial)
noseHighlight.position.set(-1, 15, 38)
doraemon.add(noseHighlight)

// 创建嘴巴（更可爱的笑脸）
const mouthGeometry = new THREE.SphereGeometry(10, 32, 32, 0, Math.PI * 2, 0, Math.PI * 0.5)
const mouthMaterial = new THREE.MeshStandardMaterial({
    color: 0x8B0000,
    roughness: 0.8,
    metalness: 0.0
})
const mouth = new THREE.Mesh(mouthGeometry, mouthMaterial)
mouth.position.set(0, 4, 33)
mouth.rotation.x = Math.PI * 0.2
mouth.scale.set(1.3, 0.7, 1.2)
mouth.castShadow = true
mouth.receiveShadow = true
doraemon.add(mouth)

// 添加舌头（更可爱的粉色）
const tongueGeometry = new THREE.SphereGeometry(5, 16, 16)
const tongueMaterial = new THREE.MeshStandardMaterial({
    color: 0xFF69B4,
    roughness: 0.6,
    metalness: 0.0
})
const tongue = new THREE.Mesh(tongueGeometry, tongueMaterial)
tongue.position.set(0, 1, 36)
tongue.scale.set(1.2, 0.4, 0.8)
doraemon.add(tongue)

// 添加牙齿
const toothGeometry = new THREE.BoxGeometry(2, 3, 1)
const toothMaterial = new THREE.MeshStandardMaterial({
    color: 0xFFFFF0,
    roughness: 0.1,
    metalness: 0.0
})
const leftTooth = new THREE.Mesh(toothGeometry, toothMaterial)
leftTooth.position.set(-3, 6, 37)
leftTooth.rotation.x = -Math.PI * 0.1
doraemon.add(leftTooth)

const rightTooth = new THREE.Mesh(toothGeometry, toothMaterial)
rightTooth.position.set(3, 6, 37)
rightTooth.rotation.x = -Math.PI * 0.1
doraemon.add(rightTooth)

// 创建胡须（更细更长，更高分辨率）
const whiskerGeometry = new THREE.CylinderGeometry(0.2, 0.2, 20, 16)
// 左边胡须
const leftWhisker1 = new THREE.Mesh(whiskerGeometry, blackMaterial)
leftWhisker1.position.set(-20, 12, 28)
leftWhisker1.rotation.z = Math.PI / 8
leftWhisker1.castShadow = true
doraemon.add(leftWhisker1)

const leftWhisker2 = new THREE.Mesh(whiskerGeometry, blackMaterial)
leftWhisker2.position.set(-20, 8, 28)
leftWhisker2.rotation.z = 0
leftWhisker2.castShadow = true
doraemon.add(leftWhisker2)

const leftWhisker3 = new THREE.Mesh(whiskerGeometry, blackMaterial)
leftWhisker3.position.set(-20, 4, 28)
leftWhisker3.rotation.z = -Math.PI / 8
leftWhisker3.castShadow = true
doraemon.add(leftWhisker3)

// 右边胡须
const rightWhisker1 = new THREE.Mesh(whiskerGeometry, blackMaterial)
rightWhisker1.position.set(20, 12, 28)
rightWhisker1.rotation.z = -Math.PI / 8
rightWhisker1.castShadow = true
doraemon.add(rightWhisker1)

const rightWhisker2 = new THREE.Mesh(whiskerGeometry, blackMaterial)
rightWhisker2.position.set(20, 8, 28)
rightWhisker2.rotation.z = 0
rightWhisker2.castShadow = true
doraemon.add(rightWhisker2)

const rightWhisker3 = new THREE.Mesh(whiskerGeometry, blackMaterial)
rightWhisker3.position.set(20, 4, 28)
rightWhisker3.rotation.z = Math.PI / 8
rightWhisker3.castShadow = true
doraemon.add(rightWhisker3)

// 创建身体（更圆润可爱的身材）
const bodyGeometry = new THREE.SphereGeometry(30, 64, 64)
const body = new THREE.Mesh(bodyGeometry, blueMaterial)
body.position.set(0, -25, 0)
body.scale.set(1, 1.3, 1)
body.castShadow = true
body.receiveShadow = true
doraemon.add(body)

// 创建肚子（更大更突出的白色部分）
const bellyGeometry = new THREE.SphereGeometry(26, 64, 64)
const belly = new THREE.Mesh(bellyGeometry, whiteMaterial)
belly.position.set(0, -25, 18)
belly.scale.set(1, 1.3, 0.6)
belly.castShadow = true
belly.receiveShadow = true
doraemon.add(belly)

// 创建口袋（更大更明显，带深度）
const pocketGeometry = new THREE.CylinderGeometry(15, 14, 3, 32)
const pocketMaterial = new THREE.MeshStandardMaterial({ 
    color: 0x000000,
    roughness: 0.9,
    metalness: 0.0
})
const pocket = new THREE.Mesh(pocketGeometry, pocketMaterial)
pocket.position.set(0, -30, 25)
pocket.rotation.x = Math.PI / 2
pocket.castShadow = true
pocket.receiveShadow = true
doraemon.add(pocket)

// 创建口袋边缘
const pocketEdgeGeometry = new THREE.TorusGeometry(15, 1, 16, 32)
const pocketEdge = new THREE.Mesh(pocketEdgeGeometry, blackMaterial)
pocketEdge.position.set(0, -28.5, 28)
pocketEdge.castShadow = true
doraemon.add(pocketEdge)

// 创建铃铛（更精细的金属效果）
const bellGeometry = new THREE.SphereGeometry(5, 32, 32)
const bell = new THREE.Mesh(bellGeometry, yellowMaterial)
bell.position.set(0, 8, 32)
bell.castShadow = true
bell.receiveShadow = true
doraemon.add(bell)

// 添加铃铛的细节
const bellCenterGeometry = new THREE.SphereGeometry(1, 16, 16)
const bellCenter = new THREE.Mesh(bellCenterGeometry, blackMaterial)
bellCenter.position.set(0, 8, 37)
doraemon.add(bellCenter)

// 创建铃铛上的线（红色项圈）
const collarGeometry = new THREE.TorusGeometry(18, 2, 16, 64)
const collar = new THREE.Mesh(collarGeometry, redMaterial)
collar.position.set(0, 8, 0)
collar.rotation.x = Math.PI / 2
collar.castShadow = true
collar.receiveShadow = true
doraemon.add(collar)

// 创建手臂（更可爱的圆润手臂）
const armGeometry = new THREE.CapsuleGeometry(7, 18, 16, 32)
const leftArm = new THREE.Mesh(armGeometry, blueMaterial)
leftArm.position.set(-32, -15, 0)
leftArm.rotation.z = Math.PI / 8
leftArm.castShadow = true
leftArm.receiveShadow = true
doraemon.add(leftArm)

const rightArm = new THREE.Mesh(armGeometry, blueMaterial)
rightArm.position.set(32, -15, 0)
rightArm.rotation.z = -Math.PI / 8
rightArm.castShadow = true
rightArm.receiveShadow = true
doraemon.add(rightArm)

// 创建手（更大更圆的白色手套）
const handGeometry = new THREE.SphereGeometry(10, 32, 32)
const leftHand = new THREE.Mesh(handGeometry, whiteMaterial)
leftHand.position.set(-38, -28, 0)
leftHand.scale.set(1, 0.8, 1.2)
leftHand.castShadow = true
leftHand.receiveShadow = true
doraemon.add(leftHand)

const rightHand = new THREE.Mesh(handGeometry, whiteMaterial)
rightHand.position.set(38, -28, 0)
rightHand.scale.set(1, 0.8, 1.2)
rightHand.castShadow = true
rightHand.receiveShadow = true
doraemon.add(rightHand)

// 添加更可爱的手指细节
const fingerGeometry = new THREE.SphereGeometry(2.5, 16, 16)
for (let i = 0; i < 4; i++) {
    // 左手手指
    const leftFinger = new THREE.Mesh(fingerGeometry, whiteMaterial)
    leftFinger.position.set(-38 + (i - 1.5) * 2.5, -22, 8)
    leftFinger.scale.set(0.7, 1.8, 0.7)
    leftFinger.castShadow = true
    doraemon.add(leftFinger)

    // 右手手指
    const rightFinger = new THREE.Mesh(fingerGeometry, whiteMaterial)
    rightFinger.position.set(38 + (i - 1.5) * 2.5, -22, 8)
    rightFinger.scale.set(0.7, 1.8, 0.7)
    rightFinger.castShadow = true
    doraemon.add(rightFinger)
}

// 添加拇指
const thumbGeometry = new THREE.SphereGeometry(2, 16, 16)
const leftThumb = new THREE.Mesh(thumbGeometry, whiteMaterial)
leftThumb.position.set(-32, -25, 8)
leftThumb.scale.set(1, 1.5, 1)
leftThumb.castShadow = true
doraemon.add(leftThumb)

const rightThumb = new THREE.Mesh(thumbGeometry, whiteMaterial)
rightThumb.position.set(32, -25, 8)
rightThumb.scale.set(1, 1.5, 1)
rightThumb.castShadow = true
doraemon.add(rightThumb)

// 创建腿（更可爱的短腿）
const legGeometry = new THREE.CylinderGeometry(9, 9, 15, 32)
const leftLeg = new THREE.Mesh(legGeometry, blueMaterial)
leftLeg.position.set(-12, -55, 0)
leftLeg.castShadow = true
leftLeg.receiveShadow = true
doraemon.add(leftLeg)

const rightLeg = new THREE.Mesh(legGeometry, blueMaterial)
rightLeg.position.set(12, -55, 0)
rightLeg.castShadow = true
rightLeg.receiveShadow = true
doraemon.add(rightLeg)

// 创建脚（更大更可爱的白色脚）
const footGeometry = new THREE.SphereGeometry(14, 32, 32)
const leftFoot = new THREE.Mesh(footGeometry, whiteMaterial)
leftFoot.position.set(-12, -68, 10)
leftFoot.scale.set(1.3, 0.5, 2)
leftFoot.castShadow = true
leftFoot.receiveShadow = true
doraemon.add(leftFoot)

const rightFoot = new THREE.Mesh(footGeometry, whiteMaterial)
rightFoot.position.set(12, -68, 10)
rightFoot.scale.set(1.3, 0.5, 2)
rightFoot.castShadow = true
rightFoot.receiveShadow = true
doraemon.add(rightFoot)

// 添加脚趾细节
const toeGeometry = new THREE.SphereGeometry(2, 16, 16)
for (let i = 0; i < 3; i++) {
    // 左脚脚趾
    const leftToe = new THREE.Mesh(toeGeometry, whiteMaterial)
    leftToe.position.set(-12 + (i - 1) * 3, -66, 22)
    leftToe.scale.set(1, 0.8, 1.2)
    leftToe.castShadow = true
    doraemon.add(leftToe)

    // 右脚脚趾
    const rightToe = new THREE.Mesh(toeGeometry, whiteMaterial)
    rightToe.position.set(12 + (i - 1) * 3, -66, 22)
    rightToe.scale.set(1, 0.8, 1.2)
    rightToe.castShadow = true
    doraemon.add(rightToe)
}

// 创建尾巴（更可爱的小尾巴）
const tailGeometry = new THREE.SphereGeometry(3, 32, 32)
const tail = new THREE.Mesh(tailGeometry, redMaterial)
tail.position.set(0, -20, -30)
tail.castShadow = true
tail.receiveShadow = true
doraemon.add(tail)

// 添加腮红
const blushGeometry = new THREE.SphereGeometry(4, 16, 16)
const blushMaterial = new THREE.MeshStandardMaterial({
    color: 0xFFB6C1,
    roughness: 0.8,
    metalness: 0.0,
    transparent: true,
    opacity: 0.6
})
const leftBlush = new THREE.Mesh(blushGeometry, blushMaterial)
leftBlush.position.set(-25, 8, 25)
leftBlush.scale.set(1.5, 1, 0.5)
doraemon.add(leftBlush)

const rightBlush = new THREE.Mesh(blushGeometry, blushMaterial)
rightBlush.position.set(25, 8, 25)
rightBlush.scale.set(1.5, 1, 0.5)
doraemon.add(rightBlush)

// 添加眉毛
const eyebrowGeometry = new THREE.CylinderGeometry(0.5, 0.5, 8, 8)
const eyebrowMaterial = new THREE.MeshStandardMaterial({
    color: 0x000000,
    roughness: 0.8,
    metalness: 0.0
})
const leftEyebrow = new THREE.Mesh(eyebrowGeometry, eyebrowMaterial)
leftEyebrow.position.set(-13, 32, 28)
leftEyebrow.rotation.z = Math.PI / 6
leftEyebrow.rotation.x = Math.PI / 2
leftEyebrow.castShadow = true
doraemon.add(leftEyebrow)

const rightEyebrow = new THREE.Mesh(eyebrowGeometry, eyebrowMaterial)
rightEyebrow.position.set(13, 32, 28)
rightEyebrow.rotation.z = -Math.PI / 6
rightEyebrow.rotation.x = Math.PI / 2
rightEyebrow.castShadow = true
doraemon.add(rightEyebrow)

// 将哆啦A梦添加到场景
scene.add(doraemon)

// 创建地面（带纹理效果）
const groundGeometry = new THREE.PlaneGeometry(300, 300, 50, 50)
const groundMaterial = new THREE.MeshStandardMaterial({ 
    color: 0x90EE90,
    roughness: 0.8,
    metalness: 0.0
})
const ground = new THREE.Mesh(groundGeometry, groundMaterial)
ground.rotation.x = -Math.PI / 2
ground.position.y = -80
ground.receiveShadow = true
scene.add(ground)

// 添加环境贴图
const cubeLoader = new THREE.CubeTextureLoader()
const envMap = cubeLoader.load([
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=='
])

// 应用环境贴图到材质
blueMaterial.envMap = envMap
whiteMaterial.envMap = envMap
yellowMaterial.envMap = envMap

// 创建控制器
const controls = new OrbitControls(camera, renderer.domElement)
controls.enableDamping = true
controls.dampingFactor = 0.05
controls.minDistance = 50
controls.maxDistance = 400
controls.autoRotate = true
controls.autoRotateSpeed = 0.5

// 创建性能监控
const stats = new Stats()
document.body.appendChild(stats.dom)

// 添加辅助坐标轴
const axesHelper = new THREE.AxesHelper(50)
scene.add(axesHelper)

// 动画循环
function animate() {
    requestAnimationFrame(animate)

    // 让眼珠轻微移动（更新位置）
    const time = Date.now() * 0.001
    leftEyeBall.position.x = -13 + Math.sin(time * 0.5) * 1.5
    rightEyeBall.position.x = 13 + Math.sin(time * 0.5) * 1.5
    leftEyeBall.position.y = 22 + Math.cos(time * 0.3) * 0.5
    rightEyeBall.position.y = 22 + Math.cos(time * 0.3) * 0.5

    // 同步眼睛高光
    leftHighlight.position.x = -12 + Math.sin(time * 0.5) * 1.5
    rightHighlight.position.x = 14 + Math.sin(time * 0.5) * 1.5
    leftHighlight.position.y = 24 + Math.cos(time * 0.3) * 0.5
    rightHighlight.position.y = 24 + Math.cos(time * 0.3) * 0.5

    // 让铃铛轻微摆动
    bell.rotation.z = Math.sin(time * 2) * 0.15
    bellCenter.rotation.z = Math.sin(time * 2) * 0.15

    // 让尾巴摆动
    tail.rotation.y = Math.sin(time * 3) * 0.3
    tail.rotation.x = Math.sin(time * 2) * 0.1

    // 让手臂轻微摆动（更新角度）
    leftArm.rotation.z = Math.PI / 8 + Math.sin(time * 1.5) * 0.15
    rightArm.rotation.z = -Math.PI / 8 - Math.sin(time * 1.5) * 0.15

    // 让舌头轻微动画
    tongue.scale.y = 0.4 + Math.sin(time * 4) * 0.1

    // 让整个身体轻微呼吸动画
    doraemon.scale.y = 1 + Math.sin(time * 1.2) * 0.02

    // 让鼻子高光闪烁
    noseHighlight.material.opacity = 0.7 + Math.sin(time * 3) * 0.3

    // 让眼睛偶尔眨眼
    if (Math.sin(time * 0.8) > 0.95) {
        leftEye.scale.y = 0.1
        rightEye.scale.y = 0.1
    } else {
        leftEye.scale.y = 1.3
        rightEye.scale.y = 1.3
    }

    controls.update()
    renderer.render(scene, camera)
    stats.update()
}

// 窗口大小调整
window.addEventListener('resize', () => {
    camera.aspect = window.innerWidth / window.innerHeight
    camera.updateProjectionMatrix()
    renderer.setSize(window.innerWidth, window.innerHeight)
})

// 启动动画
animate()

console.log('专业级哆啦A梦3D模型创建完成！🤖✨🎨') 